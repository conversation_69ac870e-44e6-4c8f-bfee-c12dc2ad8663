{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/shared-cache-controls.ts"], "sourcesContent": ["import type { PrerenderManifest } from '../../../build'\nimport type { <PERSON><PERSON><PERSON>only } from '../../../shared/lib/deep-readonly'\nimport type { CacheControl } from '../cache-control'\n\n/**\n * A shared cache of cache controls for routes. This cache is used so we don't\n * have to modify the prerender manifest when we want to update the cache\n * control for a route.\n */\nexport class SharedCacheControls {\n  /**\n   * The in-memory cache of cache lives for routes. This cache is populated when\n   * the cache is updated with new cache lives.\n   */\n  private static readonly cacheControls = new Map<string, CacheControl>()\n\n  constructor(\n    /**\n     * The prerender manifest that contains the initial cache controls for\n     * routes.\n     */\n    private readonly prerenderManifest: DeepReadonly<\n      Pick<PrerenderManifest, 'routes' | 'dynamicRoutes'>\n    >\n  ) {}\n\n  /**\n   * Try to get the cache control value for a route. This will first try to get\n   * the value from the in-memory cache. If the value is not present in the\n   * in-memory cache, it will be sourced from the prerender manifest.\n   *\n   * @param route the route to get the cache control for\n   * @returns the cache control for the route, or undefined if the values\n   *          are not present in the in-memory cache or the prerender manifest\n   */\n  public get(route: string): CacheControl | undefined {\n    // This is a copy on write cache that is updated when the cache is updated.\n    // If the cache is never written to, then the values will be sourced from\n    // the prerender manifest.\n    let cacheControl = SharedCacheControls.cacheControls.get(route)\n    if (cacheControl) return cacheControl\n\n    let prerenderData = this.prerenderManifest.routes[route]\n\n    if (prerenderData) {\n      const { initialRevalidateSeconds, initialExpireSeconds } = prerenderData\n\n      if (typeof initialRevalidateSeconds !== 'undefined') {\n        return {\n          revalidate: initialRevalidateSeconds,\n          expire: initialExpireSeconds,\n        }\n      }\n    }\n\n    const dynamicPrerenderData = this.prerenderManifest.dynamicRoutes[route]\n\n    if (dynamicPrerenderData) {\n      const { fallbackRevalidate, fallbackExpire } = dynamicPrerenderData\n\n      if (typeof fallbackRevalidate !== 'undefined') {\n        return { revalidate: fallbackRevalidate, expire: fallbackExpire }\n      }\n    }\n\n    return undefined\n  }\n\n  /**\n   * Set the cache control for a route.\n   *\n   * @param route the route to set the cache control for\n   * @param cacheControl the cache control for the route\n   */\n  public set(route: string, cacheControl: CacheControl) {\n    SharedCacheControls.cacheControls.set(route, cacheControl)\n  }\n\n  /**\n   * Clear the in-memory cache of cache controls for routes.\n   */\n  public clear() {\n    SharedCacheControls.cacheControls.clear()\n  }\n}\n"], "names": ["SharedCacheControls", "cacheControls", "Map", "constructor", "prerenderManifest", "get", "route", "cacheControl", "prerenderData", "routes", "initialRevalidateSeconds", "initialExpireSeconds", "revalidate", "expire", "dynamicPrerenderData", "dynamicRoutes", "fallbackRevalidate", "fallbackExpire", "undefined", "set", "clear"], "mappings": "AAIA;;;;CAIC,GACD,OAAO,MAAMA;gBACX;;;GAGC,QACuBC,gBAAgB,IAAIC;IAE5CC,YACE;;;KAGC,GACD,AAAiBC,iBAEhB,CACD;aAHiBA,oBAAAA;IAGhB;IAEH;;;;;;;;GAQC,GACD,AAAOC,IAAIC,KAAa,EAA4B;QAClD,2EAA2E;QAC3E,yEAAyE;QACzE,0BAA0B;QAC1B,IAAIC,eAAeP,oBAAoBC,aAAa,CAACI,GAAG,CAACC;QACzD,IAAIC,cAAc,OAAOA;QAEzB,IAAIC,gBAAgB,IAAI,CAACJ,iBAAiB,CAACK,MAAM,CAACH,MAAM;QAExD,IAAIE,eAAe;YACjB,MAAM,EAAEE,wBAAwB,EAAEC,oBAAoB,EAAE,GAAGH;YAE3D,IAAI,OAAOE,6BAA6B,aAAa;gBACnD,OAAO;oBACLE,YAAYF;oBACZG,QAAQF;gBACV;YACF;QACF;QAEA,MAAMG,uBAAuB,IAAI,CAACV,iBAAiB,CAACW,aAAa,CAACT,MAAM;QAExE,IAAIQ,sBAAsB;YACxB,MAAM,EAAEE,kBAAkB,EAAEC,cAAc,EAAE,GAAGH;YAE/C,IAAI,OAAOE,uBAAuB,aAAa;gBAC7C,OAAO;oBAAEJ,YAAYI;oBAAoBH,QAAQI;gBAAe;YAClE;QACF;QAEA,OAAOC;IACT;IAEA;;;;;GAKC,GACD,AAAOC,IAAIb,KAAa,EAAEC,YAA0B,EAAE;QACpDP,oBAAoBC,aAAa,CAACkB,GAAG,CAACb,OAAOC;IAC/C;IAEA;;GAEC,GACD,AAAOa,QAAQ;QACbpB,oBAAoBC,aAAa,CAACmB,KAAK;IACzC;AACF"}