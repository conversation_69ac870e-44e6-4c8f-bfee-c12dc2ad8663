{"version": 3, "sources": ["../../../src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children"], "mappings": "AAAA;;AAEA,OAAOA,SAASC,UAAU,QAAkB,QAAO;AACnD,SAASC,eAAe,QAAQ,qDAAoD;AAEpF,eAAe,SAASC;IACtB,MAAMC,WAAWH,WAAWC;IAC5B,qBAAO;kBAAGE;;AACZ"}