import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Delete all chat sessions for the user (messages will be deleted automatically due to cascade)
    const deleteResult = await prisma.chatSession.deleteMany({
      where: {
        userId: session.user.id
      }
    })

    return NextResponse.json({ 
      message: "All chat sessions deleted successfully",
      deletedCount: deleteResult.count
    })

  } catch (error) {
    console.error("Delete all chat sessions error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
