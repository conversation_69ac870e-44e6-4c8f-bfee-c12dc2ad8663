{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/hooks/use-measure-height.ts"], "sourcesContent": ["import { useEffect, useState } from 'react'\n\nexport function useMeasureHeight(\n  ref: React.RefObject<HTMLDivElement | null>\n): [number, boolean] {\n  const [pristine, setPristine] = useState<boolean>(true)\n  const [height, setHeight] = useState<number>(0)\n\n  useEffect(() => {\n    const el = ref.current\n\n    if (!el) {\n      return\n    }\n\n    const observer = new ResizeObserver(() => {\n      const { height: h } = el.getBoundingClientRect()\n      setHeight((prevHeight) => {\n        if (prevHeight !== 0) {\n          setPristine(false)\n        }\n        return h\n      })\n    })\n\n    observer.observe(el)\n    return () => {\n      observer.disconnect()\n      setPristine(true)\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [])\n\n  return [height, pristine]\n}\n"], "names": ["useEffect", "useState", "useMeasureHeight", "ref", "pristine", "setPristine", "height", "setHeight", "el", "current", "observer", "ResizeObserver", "h", "getBoundingClientRect", "prevHeight", "observe", "disconnect"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,QAAO;AAE3C,OAAO,SAASC,iBACdC,GAA2C;IAE3C,MAAM,CAACC,UAAUC,YAAY,GAAGJ,SAAkB;IAClD,MAAM,CAACK,QAAQC,UAAU,GAAGN,SAAiB;IAE7CD,UAAU;QACR,MAAMQ,KAAKL,IAAIM,OAAO;QAEtB,IAAI,CAACD,IAAI;YACP;QACF;QAEA,MAAME,WAAW,IAAIC,eAAe;YAClC,MAAM,EAAEL,QAAQM,CAAC,EAAE,GAAGJ,GAAGK,qBAAqB;YAC9CN,UAAU,CAACO;gBACT,IAAIA,eAAe,GAAG;oBACpBT,YAAY;gBACd;gBACA,OAAOO;YACT;QACF;QAEAF,SAASK,OAAO,CAACP;QACjB,OAAO;YACLE,SAASM,UAAU;YACnBX,YAAY;QACd;IACA,uDAAuD;IACzD,GAAG,EAAE;IAEL,OAAO;QAACC;QAAQF;KAAS;AAC3B"}