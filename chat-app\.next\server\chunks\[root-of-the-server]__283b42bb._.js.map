{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON>rovider from \"next-auth/providers/credentials\"\nimport { PrismaAdapter } from \"@auth/prisma-adapter\"\nimport { prisma } from \"./prisma\"\nimport bcrypt from \"bcryptjs\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.password) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: \"jwt\"\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n    signUp: \"/auth/signup\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n      }\n      return session\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC5B;YACA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\"\nimport { getServerSession } from \"next-auth\"\nimport { authOptions } from \"@/lib/auth\"\nimport { prisma } from \"@/lib/prisma\"\nimport axios from \"axios\"\n\nconst CHAT_API_URL = process.env.CHAT_API_URL || \"https://studio.cobraaisystems.com/api/v1/prediction/149d3377-d718-4d99-8f06-6441987bf400\"\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: \"Unauthorized\" },\n        { status: 401 }\n      )\n    }\n\n    const { message, chatSessionId } = await request.json()\n\n    if (!message) {\n      return NextResponse.json(\n        { error: \"Message is required\" },\n        { status: 400 }\n      )\n    }\n\n    let sessionId = chatSessionId\n\n    // Create new chat session if none provided\n    if (!sessionId) {\n      const newSession = await prisma.chatSession.create({\n        data: {\n          userId: session.user.id,\n          title: message.substring(0, 50) + (message.length > 50 ? \"...\" : \"\")\n        }\n      })\n      sessionId = newSession.id\n    }\n\n    // Save user message\n    await prisma.chatMessage.create({\n      data: {\n        chatSessionId: sessionId,\n        content: message,\n        role: \"user\"\n      }\n    })\n\n    // Call external API\n    const response = await axios.post(CHAT_API_URL, {\n      question: message\n    })\n\n    const assistantMessage = response.data\n\n    // Save assistant response\n    await prisma.chatMessage.create({\n      data: {\n        chatSessionId: sessionId,\n        content: JSON.stringify(assistantMessage),\n        role: \"assistant\"\n      }\n    })\n\n    return NextResponse.json({\n      message: assistantMessage,\n      chatSessionId: sessionId\n    })\n\n  } catch (error) {\n    console.error(\"Chat API error:\", error)\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: \"Unauthorized\" },\n        { status: 401 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const chatSessionId = searchParams.get(\"sessionId\")\n\n    if (!chatSessionId) {\n      // Get all chat sessions for user\n      const chatSessions = await prisma.chatSession.findMany({\n        where: { userId: session.user.id },\n        orderBy: { updatedAt: \"desc\" },\n        include: {\n          messages: {\n            orderBy: { createdAt: \"asc\" }\n          }\n        }\n      })\n\n      return NextResponse.json({ chatSessions })\n    } else {\n      // Get specific chat session with messages\n      const chatSession = await prisma.chatSession.findFirst({\n        where: {\n          id: chatSessionId,\n          userId: session.user.id\n        },\n        include: {\n          messages: {\n            orderBy: { createdAt: \"asc\" }\n          }\n        }\n      })\n\n      if (!chatSession) {\n        return NextResponse.json(\n          { error: \"Chat session not found\" },\n          { status: 404 }\n        )\n      }\n\n      return NextResponse.json({ chatSession })\n    }\n\n  } catch (error) {\n    console.error(\"Chat GET API error:\", error)\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY,IAAI;AAE1C,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,IAAI;QAErD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,YAAY;QAEhB,2CAA2C;QAC3C,IAAI,CAAC,WAAW;YACd,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBACjD,MAAM;oBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,OAAO,QAAQ,SAAS,CAAC,GAAG,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,QAAQ,EAAE;gBACrE;YACF;YACA,YAAY,WAAW,EAAE;QAC3B;QAEA,oBAAoB;QACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,eAAe;gBACf,SAAS;gBACT,MAAM;YACR;QACF;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,cAAc;YAC9C,UAAU;QACZ;QAEA,MAAM,mBAAmB,SAAS,IAAI;QAEtC,0BAA0B;QAC1B,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,eAAe;gBACf,SAAS,KAAK,SAAS,CAAC;gBACxB,MAAM;YACR;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,eAAe;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC;QAEvC,IAAI,CAAC,eAAe;YAClB,iCAAiC;YACjC,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACrD,OAAO;oBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBAAC;gBACjC,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,SAAS;oBACP,UAAU;wBACR,SAAS;4BAAE,WAAW;wBAAM;oBAC9B;gBACF;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAa;QAC1C,OAAO;YACL,0CAA0C;YAC1C,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBACrD,OAAO;oBACL,IAAI;oBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACzB;gBACA,SAAS;oBACP,UAAU;wBACR,SAAS;4BAAE,WAAW;wBAAM;oBAC9B;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyB,GAClC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAY;QACzC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}