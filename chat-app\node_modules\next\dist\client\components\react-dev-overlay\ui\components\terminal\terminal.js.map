{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/terminal/terminal.tsx"], "sourcesContent": ["import Anser from 'next/dist/compiled/anser'\nimport * as React from 'react'\nimport { HotlinkedText } from '../hot-linked-text'\nimport { EditorLink } from './editor-link'\nimport { ExternalIcon } from '../../icons/external'\nimport { getFrameSource } from '../../../utils/stack-frame'\nimport { useOpenInEditor } from '../../utils/use-open-in-editor'\nimport { FileIcon } from '../../icons/file'\n\nexport type TerminalProps = { content: string }\n\nfunction getFile(lines: string[]) {\n  const contentFileName = lines.shift()\n  if (!contentFileName) return null\n  const [fileName, line, column] = contentFileName.split(':', 3)\n\n  const parsedLine = Number(line)\n  const parsedColumn = Number(column)\n  const hasLocation = !Number.isNaN(parsedLine) && !Number.isNaN(parsedColumn)\n\n  return {\n    fileName: hasLocation ? fileName : contentFileName,\n    location: hasLocation\n      ? {\n          line: parsedLine,\n          column: parsedColumn,\n        }\n      : undefined,\n  }\n}\n\nfunction getImportTraceFiles(lines: string[]) {\n  if (\n    lines.some((line) => /ReactServerComponentsError:/.test(line)) ||\n    lines.some((line) => /Import trace for requested module:/.test(line))\n  ) {\n    // Grab the lines at the end containing the files\n    const files = []\n    while (\n      /.+\\..+/.test(lines[lines.length - 1]) &&\n      !lines[lines.length - 1].includes(':')\n    ) {\n      const file = lines.pop()!.trim()\n      files.unshift(file)\n    }\n\n    return files\n  }\n\n  return []\n}\n\nfunction getEditorLinks(content: string) {\n  const lines = content.split('\\n')\n  const file = getFile(lines)\n  const importTraceFiles = getImportTraceFiles(lines)\n\n  return { file, source: lines.join('\\n'), importTraceFiles }\n}\n\nexport const Terminal: React.FC<TerminalProps> = function Terminal({\n  content,\n}) {\n  const { file, source, importTraceFiles } = React.useMemo(\n    () => getEditorLinks(content),\n    [content]\n  )\n\n  const decoded = React.useMemo(() => {\n    return Anser.ansiToJson(source, {\n      json: true,\n      use_classes: true,\n      remove_empty: true,\n    })\n  }, [source])\n\n  const open = useOpenInEditor({\n    file: file?.fileName,\n    lineNumber: file?.location?.line ?? 1,\n    column: file?.location?.column ?? 0,\n  })\n\n  const stackFrame = {\n    file: file?.fileName ?? null,\n    methodName: '',\n    arguments: [],\n    lineNumber: file?.location?.line ?? null,\n    column: file?.location?.column ?? null,\n  }\n\n  const fileExtension = stackFrame?.file?.split('.').pop()\n\n  return (\n    <div data-nextjs-codeframe>\n      <div className=\"code-frame-header\">\n        <div className=\"code-frame-link\">\n          <span className=\"code-frame-icon\">\n            <FileIcon lang={fileExtension} />\n          </span>\n          <span data-text>\n            {/* TODO: Unlike the CodeFrame component, the `methodName` is unavailable. */}\n            {getFrameSource(stackFrame)}\n          </span>\n          <button\n            aria-label=\"Open in editor\"\n            data-with-open-in-editor-link-source-file\n            onClick={open}\n          >\n            <span className=\"code-frame-icon\" data-icon=\"right\">\n              <ExternalIcon width={16} height={16} />\n            </span>\n          </button>\n        </div>\n      </div>\n      <pre className=\"code-frame-pre\">\n        {decoded.map((entry, index) => (\n          <span\n            key={`terminal-entry-${index}`}\n            style={{\n              color: entry.fg ? `var(--color-${entry.fg})` : undefined,\n              ...(entry.decoration === 'bold'\n                ? // TODO(jiwon): This used to be 800, but the symbols like `─┬─` are\n                  // having longer width than expected on Geist Mono font-weight\n                  // above 600, hence a temporary fix is to use 500 for bold.\n                  { fontWeight: 500 }\n                : entry.decoration === 'italic'\n                  ? { fontStyle: 'italic' }\n                  : undefined),\n            }}\n          >\n            <HotlinkedText text={entry.content} />\n          </span>\n        ))}\n        {importTraceFiles.map((importTraceFile) => (\n          <EditorLink\n            isSourceFile={false}\n            key={importTraceFile}\n            file={importTraceFile}\n          />\n        ))}\n      </pre>\n    </div>\n  )\n}\n\nexport const TERMINAL_STYLES = `\n  [data-nextjs-terminal]::selection,\n  [data-nextjs-terminal] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n\n  [data-nextjs-terminal] * {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n\n  [data-nextjs-terminal] > div > p {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    cursor: pointer;\n    margin: 0;\n  }\n  [data-nextjs-terminal] > div > p:hover {\n    text-decoration: underline dotted;\n  }\n  [data-nextjs-terminal] div > pre {\n    overflow: hidden;\n    display: inline-block;\n  }\n`\n"], "names": ["TERMINAL_STYLES", "Terminal", "getFile", "lines", "contentFileName", "shift", "fileName", "line", "column", "split", "parsedLine", "Number", "parsedColumn", "hasLocation", "isNaN", "location", "undefined", "getImportTraceFiles", "some", "test", "files", "length", "includes", "file", "pop", "trim", "unshift", "getEditorLinks", "content", "importTraceFiles", "source", "join", "stackFrame", "React", "useMemo", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "useOpenInEditor", "lineNumber", "methodName", "arguments", "fileExtension", "div", "data-nextjs-codeframe", "className", "span", "FileIcon", "lang", "data-text", "getFrameSource", "button", "aria-label", "data-with-open-in-editor-link-source-file", "onClick", "data-icon", "ExternalIcon", "width", "height", "pre", "map", "entry", "index", "style", "color", "fg", "decoration", "fontWeight", "fontStyle", "HotlinkedText", "text", "importTraceFile", "EditorLink", "isSourceFile"], "mappings": ";;;;;;;;;;;;;;;IAiJaA,eAAe;eAAfA;;IArFAC,QAAQ;eAARA;;;;;;gEA5DK;iEACK;+BACO;4BACH;0BACE;4BACE;iCACC;sBACP;AAIzB,SAASC,QAAQC,KAAe;IAC9B,MAAMC,kBAAkBD,MAAME,KAAK;IACnC,IAAI,CAACD,iBAAiB,OAAO;IAC7B,MAAM,CAACE,UAAUC,MAAMC,OAAO,GAAGJ,gBAAgBK,KAAK,CAAC,KAAK;IAE5D,MAAMC,aAAaC,OAAOJ;IAC1B,MAAMK,eAAeD,OAAOH;IAC5B,MAAMK,cAAc,CAACF,OAAOG,KAAK,CAACJ,eAAe,CAACC,OAAOG,KAAK,CAACF;IAE/D,OAAO;QACLN,UAAUO,cAAcP,WAAWF;QACnCW,UAAUF,cACN;YACEN,MAAMG;YACNF,QAAQI;QACV,IACAI;IACN;AACF;AAEA,SAASC,oBAAoBd,KAAe;IAC1C,IACEA,MAAMe,IAAI,CAAC,CAACX,OAAS,8BAA8BY,IAAI,CAACZ,UACxDJ,MAAMe,IAAI,CAAC,CAACX,OAAS,qCAAqCY,IAAI,CAACZ,QAC/D;QACA,iDAAiD;QACjD,MAAMa,QAAQ,EAAE;QAChB,MACE,SAASD,IAAI,CAAChB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,KACrC,CAAClB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,CAACC,QAAQ,CAAC,KAClC;YACA,MAAMC,OAAOpB,MAAMqB,GAAG,GAAIC,IAAI;YAC9BL,MAAMM,OAAO,CAACH;QAChB;QAEA,OAAOH;IACT;IAEA,OAAO,EAAE;AACX;AAEA,SAASO,eAAeC,OAAe;IACrC,MAAMzB,QAAQyB,QAAQnB,KAAK,CAAC;IAC5B,MAAMc,OAAOrB,QAAQC;IACrB,MAAM0B,mBAAmBZ,oBAAoBd;IAE7C,OAAO;QAAEoB;QAAMO,QAAQ3B,MAAM4B,IAAI,CAAC;QAAOF;IAAiB;AAC5D;AAEO,MAAM5B,WAAoC,SAASA,SAAS,KAElE;IAFkE,IAAA,EACjE2B,OAAO,EACR,GAFkE;QAkBnDL,gBACJA,iBAOIA,iBACJA,iBAGYS;IA3BtB,MAAM,EAAET,IAAI,EAAEO,MAAM,EAAED,gBAAgB,EAAE,GAAGI,OAAMC,OAAO,CACtD,IAAMP,eAAeC,UACrB;QAACA;KAAQ;IAGX,MAAMO,UAAUF,OAAMC,OAAO,CAAC;QAC5B,OAAOE,cAAK,CAACC,UAAU,CAACP,QAAQ;YAC9BQ,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAACV;KAAO;QAIGP,qBACJA;IAHV,MAAMkB,OAAOC,IAAAA,gCAAe,EAAC;QAC3BnB,IAAI,EAAEA,wBAAAA,KAAMjB,QAAQ;QACpBqC,YAAYpB,CAAAA,sBAAAA,yBAAAA,iBAAAA,KAAMR,QAAQ,qBAAdQ,eAAgBhB,IAAI,YAApBgB,sBAAwB;QACpCf,QAAQe,CAAAA,wBAAAA,yBAAAA,kBAAAA,KAAMR,QAAQ,qBAAdQ,gBAAgBf,MAAM,YAAtBe,wBAA0B;IACpC;QAGQA,gBAGMA,sBACJA;IALV,MAAMS,aAAa;QACjBT,MAAMA,CAAAA,iBAAAA,wBAAAA,KAAMjB,QAAQ,YAAdiB,iBAAkB;QACxBqB,YAAY;QACZC,WAAW,EAAE;QACbF,YAAYpB,CAAAA,uBAAAA,yBAAAA,kBAAAA,KAAMR,QAAQ,qBAAdQ,gBAAgBhB,IAAI,YAApBgB,uBAAwB;QACpCf,QAAQe,CAAAA,yBAAAA,yBAAAA,kBAAAA,KAAMR,QAAQ,qBAAdQ,gBAAgBf,MAAM,YAAtBe,yBAA0B;IACpC;IAEA,MAAMuB,gBAAgBd,+BAAAA,mBAAAA,WAAYT,IAAI,qBAAhBS,iBAAkBvB,KAAK,CAAC,KAAKe,GAAG;IAEtD,qBACE,sBAACuB;QAAIC,uBAAqB;;0BACxB,qBAACD;gBAAIE,WAAU;0BACb,cAAA,sBAACF;oBAAIE,WAAU;;sCACb,qBAACC;4BAAKD,WAAU;sCACd,cAAA,qBAACE,cAAQ;gCAACC,MAAMN;;;sCAElB,qBAACI;4BAAKG,WAAS;sCAEZC,IAAAA,0BAAc,EAACtB;;sCAElB,qBAACuB;4BACCC,cAAW;4BACXC,2CAAyC;4BACzCC,SAASjB;sCAET,cAAA,qBAACS;gCAAKD,WAAU;gCAAkBU,aAAU;0CAC1C,cAAA,qBAACC,sBAAY;oCAACC,OAAO;oCAAIC,QAAQ;;;;;;;0BAKzC,sBAACC;gBAAId,WAAU;;oBACZd,QAAQ6B,GAAG,CAAC,CAACC,OAAOC,sBACnB,qBAAChB;4BAECiB,OAAO;gCACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKrD;gCAC/C,GAAIiD,MAAMK,UAAU,KAAK,SAErB,8DAA8D;gCAC9D,2DAA2D;gCAC3D;oCAAEC,YAAY;gCAAI,IAClBN,MAAMK,UAAU,KAAK,WACnB;oCAAEE,WAAW;gCAAS,IACtBxD,SAAS;4BACjB;sCAEA,cAAA,qBAACyD,4BAAa;gCAACC,MAAMT,MAAMrC,OAAO;;2BAb7B,AAAC,oBAAiBsC;oBAgB1BrC,iBAAiBmC,GAAG,CAAC,CAACW,gCACrB,qBAACC,sBAAU;4BACTC,cAAc;4BAEdtD,MAAMoD;2BADDA;;;;;AAOjB;AAEO,MAAM3E,kBAAmB"}