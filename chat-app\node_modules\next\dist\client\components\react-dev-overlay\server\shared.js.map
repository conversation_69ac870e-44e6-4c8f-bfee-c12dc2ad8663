{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/shared.ts"], "sourcesContent": ["import type { StackFrame } from 'stacktrace-parser'\nimport { codeFrameColumns } from 'next/dist/compiled/babel/code-frame'\nimport isInternal from '../../../../shared/lib/is-internal'\n\nexport interface OriginalStackFramesRequest {\n  frames: StackFrame[]\n  isServer: boolean\n  isEdgeServer: boolean\n  isAppDirectory: boolean\n}\n\nexport type OriginalStackFramesResponse = OriginalStackFrameResponseResult[]\n\nexport type OriginalStackFrameResponseResult =\n  PromiseSettledResult<OriginalStackFrameResponse>\n\nexport interface OriginalStackFrameResponse {\n  originalStackFrame?: (StackFrame & { ignored: boolean }) | null\n  originalCodeFrame?: string | null\n}\n\n/**\n * It looks up the code frame of the traced source.\n * @note It ignores Next.js/React internals, as these can often be huge bundled files.\n */\nexport function getOriginalCodeFrame(\n  frame: StackFrame,\n  source: string | null,\n  colors: boolean = process.stdout.isTTY\n): string | null {\n  if (!source || isInternal(frame.file)) {\n    return null\n  }\n\n  return codeFrameColumns(\n    source,\n    {\n      start: {\n        // 1-based, but -1 means start line without highlighting\n        line: frame.lineNumber ?? -1,\n        // 1-based, but 0 means whole line without column highlighting\n        column: frame.column ?? 0,\n      },\n    },\n    { forceColor: colors }\n  )\n}\n"], "names": ["getOriginalCodeFrame", "frame", "source", "colors", "process", "stdout", "isTTY", "isInternal", "file", "codeFrameColumns", "start", "line", "lineNumber", "column", "forceColor"], "mappings": ";;;;+BAyBgBA;;;eAAAA;;;;2BAxBiB;qEACV;AAuBhB,SAASA,qBACdC,KAAiB,EACjBC,MAAqB,EACrBC,MAAsC;IAAtCA,IAAAA,mBAAAA,SAAkBC,QAAQC,MAAM,CAACC,KAAK;IAEtC,IAAI,CAACJ,UAAUK,IAAAA,mBAAU,EAACN,MAAMO,IAAI,GAAG;QACrC,OAAO;IACT;QAOYP,mBAEEA;IAPd,OAAOQ,IAAAA,2BAAgB,EACrBP,QACA;QACEQ,OAAO;YACL,wDAAwD;YACxDC,MAAMV,CAAAA,oBAAAA,MAAMW,UAAU,YAAhBX,oBAAoB,CAAC;YAC3B,8DAA8D;YAC9DY,QAAQZ,CAAAA,gBAAAA,MAAMY,MAAM,YAAZZ,gBAAgB;QAC1B;IACF,GACA;QAAEa,YAAYX;IAAO;AAEzB"}