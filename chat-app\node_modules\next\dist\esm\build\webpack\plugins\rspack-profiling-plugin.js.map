{"version": 3, "sources": ["../../../../src/build/webpack/plugins/rspack-profiling-plugin.ts"], "sourcesContent": ["// A basic implementation to allow loaders access to loaderContext.currentTraceSpan\n\nimport type { Span } from '../../../trace'\n\nimport { getRspackCore } from '../../../shared/lib/get-rspack'\n\nconst pluginName = 'RspackProfilingPlugin'\nconst moduleSpansByCompilation = new WeakMap()\nexport const compilationSpans: WeakMap<any, Span> = new WeakMap()\n\nexport class RspackProfilingPlugin {\n  runWebpackSpan: Span\n\n  constructor({ runWebpackSpan }: { runWebpackSpan: Span }) {\n    this.runWebpackSpan = runWebpackSpan\n  }\n\n  apply(compiler: any) {\n    compiler.hooks.compilation.tap(\n      { name: pluginName, stage: -Infinity },\n      (compilation: any) => {\n        const rspack = getRspackCore()\n\n        moduleSpansByCompilation.set(compilation, new WeakMap())\n        compilationSpans.set(\n          compilation,\n          this.runWebpackSpan.traceChild('compilation-' + compilation.name)\n        )\n\n        const compilationSpan = this.runWebpackSpan.traceChild(\n          `compilation-${compilation.name}`\n        )\n\n        const moduleHooks = rspack.NormalModule.getCompilationHooks(compilation)\n        moduleHooks.loader.tap(\n          pluginName,\n          (loaderContext: any, module: any) => {\n            const moduleSpan = moduleSpansByCompilation\n              .get(compilation)\n              ?.get(module)\n            loaderContext.currentTraceSpan = moduleSpan\n          }\n        )\n\n        compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n          const span = compilationSpan.traceChild('build-module')\n          span.setAttribute('name', module.userRequest)\n          span.setAttribute('layer', module.layer)\n\n          moduleSpansByCompilation?.get(compilation)?.set(module, span)\n        })\n\n        compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n      }\n    )\n  }\n}\n"], "names": ["getRspackCore", "pluginName", "moduleSpansByCompilation", "WeakMap", "compilationSpans", "RspackProfilingPlugin", "constructor", "runWebpackSpan", "apply", "compiler", "hooks", "compilation", "tap", "name", "stage", "Infinity", "rspack", "set", "<PERSON><PERSON><PERSON><PERSON>", "compilationSpan", "moduleHooks", "NormalModule", "getCompilationHooks", "loader", "loaderContext", "module", "moduleSpan", "get", "currentTraceSpan", "buildModule", "span", "setAttribute", "userRequest", "layer", "succeedModule", "stop"], "mappings": "AAAA,mFAAmF;AAInF,SAASA,aAAa,QAAQ,iCAAgC;AAE9D,MAAMC,aAAa;AACnB,MAAMC,2BAA2B,IAAIC;AACrC,OAAO,MAAMC,mBAAuC,IAAID,UAAS;AAEjE,OAAO,MAAME;IAGXC,YAAY,EAAEC,cAAc,EAA4B,CAAE;QACxD,IAAI,CAACA,cAAc,GAAGA;IACxB;IAEAC,MAAMC,QAAa,EAAE;QACnBA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5B;YAAEC,MAAMZ;YAAYa,OAAO,CAACC;QAAS,GACrC,CAACJ;YACC,MAAMK,SAAShB;YAEfE,yBAAyBe,GAAG,CAACN,aAAa,IAAIR;YAC9CC,iBAAiBa,GAAG,CAClBN,aACA,IAAI,CAACJ,cAAc,CAACW,UAAU,CAAC,iBAAiBP,YAAYE,IAAI;YAGlE,MAAMM,kBAAkB,IAAI,CAACZ,cAAc,CAACW,UAAU,CACpD,CAAC,YAAY,EAAEP,YAAYE,IAAI,EAAE;YAGnC,MAAMO,cAAcJ,OAAOK,YAAY,CAACC,mBAAmB,CAACX;YAC5DS,YAAYG,MAAM,CAACX,GAAG,CACpBX,YACA,CAACuB,eAAoBC;oBACAvB;gBAAnB,MAAMwB,cAAaxB,gCAAAA,yBAChByB,GAAG,CAAChB,iCADYT,8BAEfyB,GAAG,CAACF;gBACRD,cAAcI,gBAAgB,GAAGF;YACnC;YAGFf,YAAYD,KAAK,CAACmB,WAAW,CAACjB,GAAG,CAACX,YAAY,CAACwB;oBAK7CvB;gBAJA,MAAM4B,OAAOX,gBAAgBD,UAAU,CAAC;gBACxCY,KAAKC,YAAY,CAAC,QAAQN,OAAOO,WAAW;gBAC5CF,KAAKC,YAAY,CAAC,SAASN,OAAOQ,KAAK;gBAEvC/B,6CAAAA,gCAAAA,yBAA0ByB,GAAG,CAAChB,iCAA9BT,8BAA4Ce,GAAG,CAACQ,QAAQK;YAC1D;YAEAnB,YAAYD,KAAK,CAACwB,aAAa,CAACtB,GAAG,CAACX,YAAY,CAACwB;oBAC/CvB,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0ByB,GAAG,CAAChB,kCAA9BT,oCAAAA,8BAA4CyB,GAAG,CAACF,4BAAhDvB,kCAAyDiC,IAAI;YAC/D;QACF;IAEJ;AACF"}