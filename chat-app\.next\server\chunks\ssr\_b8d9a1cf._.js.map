{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-red-600 text-white hover:bg-red-700\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,0CAA0C,YAAY;YACtD,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { <PERSON><PERSON> } from \"@/components/ui/Button\"\nimport Navigation from \"@/components/layout/Navigation\"\nimport { MessageCircle, Shield, Zap, Users } from \"lucide-react\"\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50\">\n      <Navigation />\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16 text-center lg:pt-32\">\n          <div className=\"mx-auto max-w-4xl\">\n            <h1 className=\"mx-auto max-w-4xl font-display text-5xl font-medium tracking-tight text-slate-900 sm:text-7xl\">\n              Intelligent Chat\n              <span className=\"relative whitespace-nowrap text-indigo-600\">\n                <svg\n                  aria-hidden=\"true\"\n                  viewBox=\"0 0 418 42\"\n                  className=\"absolute top-2/3 left-0 h-[0.58em] w-full fill-indigo-300/70\"\n                  preserveAspectRatio=\"none\"\n                >\n                  <path d=\"m203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z\" />\n                </svg>\n                <span className=\"relative\">Experience</span>\n              </span>\n            </h1>\n            <p className=\"mx-auto mt-6 max-w-2xl text-lg tracking-tight text-slate-700\">\n              Experience the future of conversation with our AI-powered chat platform.\n              Secure, intelligent, and designed for meaningful interactions.\n            </p>\n            <div className=\"mt-10 flex justify-center gap-x-6\">\n              <Link href=\"/auth/signup\">\n                <Button size=\"lg\" className=\"text-lg px-8 py-3\">\n                  Get Started\n                </Button>\n              </Link>\n              <Link href=\"/about\">\n                <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-3\">\n                  Learn More\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-24 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              Why Choose Our Platform?\n            </h2>\n            <p className=\"mt-4 text-lg text-gray-600\">\n              Built with cutting-edge technology for the best user experience\n            </p>\n          </div>\n\n          <div className=\"mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4\">\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100\">\n                <MessageCircle className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-semibold text-gray-900\">\n                Intelligent Conversations\n              </h3>\n              <p className=\"mt-2 text-gray-600\">\n                Powered by advanced AI technology for natural, meaningful conversations\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100\">\n                <Shield className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-semibold text-gray-900\">\n                Secure & Private\n              </h3>\n              <p className=\"mt-2 text-gray-600\">\n                Your conversations are protected with enterprise-grade security\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100\">\n                <Zap className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-semibold text-gray-900\">\n                Lightning Fast\n              </h3>\n              <p className=\"mt-2 text-gray-600\">\n                Get instant responses with our optimized infrastructure\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100\">\n                <Users className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <h3 className=\"mt-6 text-lg font-semibold text-gray-900\">\n                User Friendly\n              </h3>\n              <p className=\"mt-2 text-gray-600\">\n                Intuitive interface designed for users of all technical levels\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-indigo-600\">\n        <div className=\"max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n              Ready to start chatting?\n            </h2>\n            <p className=\"mt-4 text-lg text-indigo-200\">\n              Join thousands of users already experiencing the future of conversation\n            </p>\n            <div className=\"mt-8\">\n              <Link href=\"/auth/signup\">\n                <Button\n                  size=\"lg\"\n                  className=\"bg-white text-indigo-600 hover:bg-gray-50 text-lg px-8 py-3\"\n                >\n                  Start Your Journey\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>&copy; 2024 ChatApp. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAgG;kDAE5G,8OAAC;wCAAK,WAAU;;0DACd,8OAAC;gDACC,eAAY;gDACZ,SAAQ;gDACR,WAAU;gDACV,qBAAoB;0DAEpB,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;;;;;;;0CAG/B,8OAAC;gCAAE,WAAU;0CAA+D;;;;;;0CAI5E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAoB;;;;;;;;;;;kDAIlD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}