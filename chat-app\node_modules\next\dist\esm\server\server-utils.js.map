{"version": 3, "sources": ["../../src/server/server-utils.ts"], "sourcesContent": ["import type { Rewrite } from '../lib/load-custom-routes'\nimport type { RouteMatchFn } from '../shared/lib/router/utils/route-matcher'\nimport type { NextConfig } from './config'\nimport type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { getPathMatch } from '../shared/lib/router/utils/path-match'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { normalizeRscURL } from '../shared/lib/router/utils/app-paths'\nimport {\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../lib/constants'\nimport { normalizeNextQueryParam } from './web/utils'\nimport type { IncomingHttpHeaders } from 'http'\n\nexport function normalizeVercelUrl(\n  req: BaseNextRequest,\n  paramKeys: string[],\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  // make sure to normalize req.url on Vercel to strip dynamic and rewrite\n  // params from the query which are added during routing\n  const _parsedUrl = parseUrl(req.url!, true)\n  delete (_parsedUrl as any).search\n\n  for (const key of Object.keys(_parsedUrl.query)) {\n    const isNextQueryPrefix =\n      key !== NEXT_QUERY_PARAM_PREFIX && key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n\n    const isNextInterceptionMarkerPrefix =\n      key !== NEXT_INTERCEPTION_MARKER_PREFIX &&\n      key.startsWith(NEXT_INTERCEPTION_MARKER_PREFIX)\n\n    if (\n      isNextQueryPrefix ||\n      isNextInterceptionMarkerPrefix ||\n      paramKeys.includes(key) ||\n      (defaultRouteRegex && Object.keys(defaultRouteRegex.groups).includes(key))\n    ) {\n      delete _parsedUrl.query[key]\n    }\n  }\n\n  req.url = formatUrl(_parsedUrl)\n}\n\nexport function interpolateDynamicPath(\n  pathname: string,\n  params: ParsedUrlQuery,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  if (!defaultRouteRegex) return pathname\n\n  for (const param of Object.keys(defaultRouteRegex.groups)) {\n    const { optional, repeat } = defaultRouteRegex.groups[param]\n    let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n    if (optional) {\n      builtParam = `[${builtParam}]`\n    }\n\n    let paramValue: string\n    const value = params[param]\n\n    if (Array.isArray(value)) {\n      paramValue = value.map((v) => v && encodeURIComponent(v)).join('/')\n    } else if (value) {\n      paramValue = encodeURIComponent(value)\n    } else {\n      paramValue = ''\n    }\n\n    pathname = pathname.replaceAll(builtParam, paramValue)\n  }\n\n  return pathname\n}\n\nexport function normalizeDynamicRouteParams(\n  query: ParsedUrlQuery,\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex>,\n  defaultRouteMatches: ParsedUrlQuery,\n  ignoreMissingOptional: boolean\n) {\n  let hasValidParams = true\n  let params: ParsedUrlQuery = {}\n\n  for (const key of Object.keys(defaultRouteRegex.groups)) {\n    let value: string | string[] | undefined = query[key]\n\n    if (typeof value === 'string') {\n      value = normalizeRscURL(value)\n    } else if (Array.isArray(value)) {\n      value = value.map(normalizeRscURL)\n    }\n\n    // if the value matches the default value we can't rely\n    // on the parsed params, this is used to signal if we need\n    // to parse x-now-route-matches or not\n    const defaultValue = defaultRouteMatches![key]\n    const isOptional = defaultRouteRegex!.groups[key].optional\n\n    const isDefaultValue = Array.isArray(defaultValue)\n      ? defaultValue.some((defaultVal) => {\n          return Array.isArray(value)\n            ? value.some((val) => val.includes(defaultVal))\n            : value?.includes(defaultVal)\n        })\n      : value?.includes(defaultValue as string)\n\n    if (\n      isDefaultValue ||\n      (typeof value === 'undefined' && !(isOptional && ignoreMissingOptional))\n    ) {\n      return { params: {}, hasValidParams: false }\n    }\n\n    // non-provided optional values should be undefined so normalize\n    // them to undefined\n    if (\n      isOptional &&\n      (!value ||\n        (Array.isArray(value) &&\n          value.length === 1 &&\n          // fallback optional catch-all SSG pages have\n          // [[...paramName]] for the root path on Vercel\n          (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n    ) {\n      value = undefined\n      delete query[key]\n    }\n\n    // query values from the proxy aren't already split into arrays\n    // so make sure to normalize catch-all values\n    if (\n      value &&\n      typeof value === 'string' &&\n      defaultRouteRegex!.groups[key].repeat\n    ) {\n      value = value.split('/')\n    }\n\n    if (value) {\n      params[key] = value\n    }\n  }\n\n  return {\n    params,\n    hasValidParams,\n  }\n}\n\nexport function getUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n  trailingSlash,\n  caseSensitive,\n}: {\n  page: string\n  i18n?: NextConfig['i18n']\n  basePath: string\n  rewrites: {\n    fallback?: ReadonlyArray<Rewrite>\n    afterFiles?: ReadonlyArray<Rewrite>\n    beforeFiles?: ReadonlyArray<Rewrite>\n  }\n  pageIsDynamic: boolean\n  trailingSlash?: boolean\n  caseSensitive: boolean\n}) {\n  let defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n  let dynamicRouteMatcher: RouteMatchFn | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getNamedRouteRegex(page, {\n      prefixRouteKeys: false,\n    })\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(req: BaseNextRequest, parsedUrl: UrlWithParsedQuery) {\n    const rewriteParams = {}\n    let fsPathname = parsedUrl.pathname\n\n    const matchesPage = () => {\n      const fsPathnameNoSlash = removeTrailingSlash(fsPathname || '')\n      return (\n        fsPathnameNoSlash === removeTrailingSlash(page) ||\n        dynamicRouteMatcher?.(fsPathnameNoSlash)\n      )\n    }\n\n    const checkRewrite = (rewrite: Rewrite): boolean => {\n      const matcher = getPathMatch(\n        rewrite.source + (trailingSlash ? '(/)?' : ''),\n        {\n          removeUnnamedParams: true,\n          strict: true,\n          sensitive: !!caseSensitive,\n        }\n      )\n\n      if (!parsedUrl.pathname) return false\n\n      let params = matcher(parsedUrl.pathname)\n\n      if ((rewrite.has || rewrite.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          rewrite.has,\n          rewrite.missing\n        )\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        const { parsedDestination, destQuery } = prepareDestination({\n          appendParamsToQuery: true,\n          destination: rewrite.destination,\n          params: params,\n          query: parsedUrl.query,\n        })\n\n        // if the rewrite destination is external break rewrite chain\n        if (parsedDestination.protocol) {\n          return true\n        }\n\n        Object.assign(rewriteParams, destQuery, params)\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        fsPathname = parsedUrl.pathname\n        if (!fsPathname) return false\n\n        if (basePath) {\n          fsPathname = fsPathname.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const result = normalizeLocalePath(fsPathname, i18n.locales)\n          fsPathname = result.pathname\n          parsedUrl.query.nextInternalLocale =\n            result.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          return true\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            return true\n          }\n        }\n      }\n      return false\n    }\n\n    for (const rewrite of rewrites.beforeFiles || []) {\n      checkRewrite(rewrite)\n    }\n\n    if (fsPathname !== page) {\n      let finished = false\n\n      for (const rewrite of rewrites.afterFiles || []) {\n        finished = checkRewrite(rewrite)\n        if (finished) break\n      }\n\n      if (!finished && !matchesPage()) {\n        for (const rewrite of rewrites.fallback || []) {\n          finished = checkRewrite(rewrite)\n          if (finished) break\n        }\n      }\n    }\n    return rewriteParams\n  }\n\n  function getParamsFromRouteMatches(routeMatchesHeader: string) {\n    // If we don't have a default route regex, we can't get params from route\n    // matches\n    if (!defaultRouteRegex) return null\n\n    const { groups, routeKeys } = defaultRouteRegex\n\n    const matcher = getRouteMatcher({\n      re: {\n        // Simulate a RegExp match from the \\`req.url\\` input\n        exec: (str: string) => {\n          // Normalize all the prefixed query params.\n          const obj: Record<string, string> = Object.fromEntries(\n            new URLSearchParams(str)\n          )\n          for (const [key, value] of Object.entries(obj)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            obj[normalizedKey] = value\n            delete obj[key]\n          }\n\n          // Use all the named route keys.\n          const result = {} as RegExpExecArray\n          for (const keyName of Object.keys(routeKeys)) {\n            const paramName = routeKeys[keyName]\n\n            // If this param name is not a valid parameter name, then skip it.\n            if (!paramName) continue\n\n            const group = groups[paramName]\n            const value = obj[keyName]\n\n            // When we're missing a required param, we can't match the route.\n            if (!group.optional && !value) return null\n\n            result[group.pos] = value\n          }\n\n          return result\n        },\n      },\n      groups,\n    })\n\n    const routeMatches = matcher(routeMatchesHeader)\n    if (!routeMatches) return null\n\n    return routeMatches\n  }\n\n  return {\n    handleRewrites,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    getParamsFromRouteMatches,\n    /**\n     * Normalize dynamic route params.\n     *\n     * @param query - The query params to normalize.\n     * @param ignoreMissingOptional - Whether to ignore missing optional params.\n     * @returns The normalized params and whether they are valid.\n     */\n    normalizeDynamicRouteParams: (\n      query: ParsedUrlQuery,\n      ignoreMissingOptional: boolean\n    ) => {\n      if (!defaultRouteRegex || !defaultRouteMatches) {\n        return { params: {}, hasValidParams: false }\n      }\n\n      return normalizeDynamicRouteParams(\n        query,\n        defaultRouteRegex,\n        defaultRouteMatches,\n        ignoreMissingOptional\n      )\n    },\n    normalizeVercelUrl: (req: BaseNextRequest, paramKeys: string[]) =>\n      normalizeVercelUrl(req, paramKeys, defaultRouteRegex),\n    interpolateDynamicPath: (\n      pathname: string,\n      params: Record<string, undefined | string | string[]>\n    ) => interpolateDynamicPath(pathname, params, defaultRouteRegex),\n  }\n}\n\nexport function getPreviouslyRevalidatedTags(\n  headers: IncomingHttpHeaders,\n  previewModeId: string | undefined\n): string[] {\n  return typeof headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' &&\n    headers[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] === previewModeId\n    ? headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',')\n    : []\n}\n"], "names": ["format", "formatUrl", "parse", "parseUrl", "normalizeLocalePath", "getPathMatch", "getNamedRouteRegex", "getRouteMatcher", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeRscURL", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "normalizeNextQueryParam", "normalizeVercelUrl", "req", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultRouteRegex", "_parsedUrl", "url", "search", "key", "Object", "keys", "query", "isNextQueryPrefix", "startsWith", "isNextInterceptionMarkerPrefix", "includes", "groups", "interpolateDynamicPath", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "replaceAll", "normalizeDynamicRouteParams", "defaultRouteMatches", "ignoreMissingOptional", "hasValidParams", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "val", "length", "undefined", "split", "getUtils", "page", "i18n", "basePath", "rewrites", "pageIsDynamic", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "prefixRouteKeys", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "checkRewrite", "rewrite", "matcher", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "result", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "routeMatchesHeader", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "entries", "normalizedKey", "keyName", "paramName", "group", "pos", "routeMatches", "getPreviouslyRevalidatedTags", "headers", "previewModeId"], "mappings": "AAOA,SAASA,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SACEC,QAAQ,EACRC,kBAAkB,QACb,iDAAgD;AACvD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,eAAe,QAAQ,uCAAsC;AACtE,SACEC,sCAAsC,EACtCC,kCAAkC,EAClCC,+BAA+B,EAC/BC,uBAAuB,QAClB,mBAAkB;AACzB,SAASC,uBAAuB,QAAQ,cAAa;AAGrD,OAAO,SAASC,mBACdC,GAAoB,EACpBC,SAAmB,EACnBC,iBAAoE;IAEpE,wEAAwE;IACxE,uDAAuD;IACvD,MAAMC,aAAalB,SAASe,IAAII,GAAG,EAAG;IACtC,OAAO,AAACD,WAAmBE,MAAM;IAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACL,WAAWM,KAAK,EAAG;QAC/C,MAAMC,oBACJJ,QAAQT,2BAA2BS,IAAIK,UAAU,CAACd;QAEpD,MAAMe,iCACJN,QAAQV,mCACRU,IAAIK,UAAU,CAACf;QAEjB,IACEc,qBACAE,kCACAX,UAAUY,QAAQ,CAACP,QAClBJ,qBAAqBK,OAAOC,IAAI,CAACN,kBAAkBY,MAAM,EAAED,QAAQ,CAACP,MACrE;YACA,OAAOH,WAAWM,KAAK,CAACH,IAAI;QAC9B;IACF;IAEAN,IAAII,GAAG,GAAGrB,UAAUoB;AACtB;AAEA,OAAO,SAASY,uBACdC,QAAgB,EAChBC,MAAsB,EACtBf,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOc;IAE/B,KAAK,MAAME,SAASX,OAAOC,IAAI,CAACN,kBAAkBY,MAAM,EAAG;QACzD,MAAM,EAAEK,QAAQ,EAAEC,MAAM,EAAE,GAAGlB,kBAAkBY,MAAM,CAACI,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,KAAKF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,IAAIC;QACJ,MAAMC,QAAQN,MAAM,CAACC,MAAM;QAE3B,IAAIM,MAAMC,OAAO,CAACF,QAAQ;YACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;QACjE,OAAO,IAAIN,OAAO;YAChBD,aAAaM,mBAAmBL;QAClC,OAAO;YACLD,aAAa;QACf;QAEAN,WAAWA,SAASc,UAAU,CAACT,YAAYC;IAC7C;IAEA,OAAON;AACT;AAEA,OAAO,SAASe,4BACdtB,KAAqB,EACrBP,iBAAwD,EACxD8B,mBAAmC,EACnCC,qBAA8B;IAE9B,IAAIC,iBAAiB;IACrB,IAAIjB,SAAyB,CAAC;IAE9B,KAAK,MAAMX,OAAOC,OAAOC,IAAI,CAACN,kBAAkBY,MAAM,EAAG;QACvD,IAAIS,QAAuCd,KAAK,CAACH,IAAI;QAErD,IAAI,OAAOiB,UAAU,UAAU;YAC7BA,QAAQ9B,gBAAgB8B;QAC1B,OAAO,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YAC/BA,QAAQA,MAAMG,GAAG,CAACjC;QACpB;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAM0C,eAAeH,mBAAoB,CAAC1B,IAAI;QAC9C,MAAM8B,aAAalC,kBAAmBY,MAAM,CAACR,IAAI,CAACa,QAAQ;QAE1D,MAAMkB,iBAAiBb,MAAMC,OAAO,CAACU,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOf,MAAMC,OAAO,CAACF,SACjBA,MAAMe,IAAI,CAAC,CAACE,MAAQA,IAAI3B,QAAQ,CAAC0B,eACjChB,yBAAAA,MAAOV,QAAQ,CAAC0B;QACtB,KACAhB,yBAAAA,MAAOV,QAAQ,CAACsB;QAEpB,IACEE,kBACC,OAAOd,UAAU,eAAe,CAAEa,CAAAA,cAAcH,qBAAoB,GACrE;YACA,OAAO;gBAAEhB,QAAQ,CAAC;gBAAGiB,gBAAgB;YAAM;QAC7C;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEE,cACC,CAAA,CAACb,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMkB,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9ClB,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEjB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAiB,QAAQmB;YACR,OAAOjC,KAAK,CAACH,IAAI;QACnB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEiB,SACA,OAAOA,UAAU,YACjBrB,kBAAmBY,MAAM,CAACR,IAAI,CAACc,MAAM,EACrC;YACAG,QAAQA,MAAMoB,KAAK,CAAC;QACtB;QAEA,IAAIpB,OAAO;YACTN,MAAM,CAACX,IAAI,GAAGiB;QAChB;IACF;IAEA,OAAO;QACLN;QACAiB;IACF;AACF;AAEA,OAAO,SAASU,SAAS,EACvBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,aAAa,EAad;IACC,IAAIjD;IACJ,IAAIkD;IACJ,IAAIpB;IAEJ,IAAIiB,eAAe;QACjB/C,oBAAoBd,mBAAmByD,MAAM;YAC3CQ,iBAAiB;QACnB;QACAD,sBAAsB/D,gBAAgBa;QACtC8B,sBAAsBoB,oBAAoBP;IAC5C;IAEA,SAASS,eAAetD,GAAoB,EAAEuD,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAUvC,QAAQ;QAEnC,MAAM0C,cAAc;YAClB,MAAMC,oBAAoBnE,oBAAoBiE,cAAc;YAC5D,OACEE,sBAAsBnE,oBAAoBqD,UAC1CO,uCAAAA,oBAAsBO;QAE1B;QAEA,MAAMC,eAAe,CAACC;YACpB,MAAMC,UAAU3E,aACd0E,QAAQE,MAAM,GAAIb,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEc,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACf;YACf;YAGF,IAAI,CAACI,UAAUvC,QAAQ,EAAE,OAAO;YAEhC,IAAIC,SAAS6C,QAAQP,UAAUvC,QAAQ;YAEvC,IAAI,AAAC6C,CAAAA,QAAQM,GAAG,IAAIN,QAAQO,OAAO,AAAD,KAAMnD,QAAQ;gBAC9C,MAAMoD,YAAY/E,SAChBU,KACAuD,UAAU9C,KAAK,EACfoD,QAAQM,GAAG,EACXN,QAAQO,OAAO;gBAGjB,IAAIC,WAAW;oBACb9D,OAAO+D,MAAM,CAACrD,QAAQoD;gBACxB,OAAO;oBACLpD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAEsD,iBAAiB,EAAEC,SAAS,EAAE,GAAGjF,mBAAmB;oBAC1DkF,qBAAqB;oBACrBC,aAAab,QAAQa,WAAW;oBAChCzD,QAAQA;oBACRR,OAAO8C,UAAU9C,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAI8D,kBAAkBI,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEApE,OAAO+D,MAAM,CAACd,eAAegB,WAAWvD;gBACxCV,OAAO+D,MAAM,CAACf,UAAU9C,KAAK,EAAE8D,kBAAkB9D,KAAK;gBACtD,OAAO,AAAC8D,kBAA0B9D,KAAK;gBAEvCF,OAAO+D,MAAM,CAACf,WAAWgB;gBAEzBd,aAAaF,UAAUvC,QAAQ;gBAC/B,IAAI,CAACyC,YAAY,OAAO;gBAExB,IAAIV,UAAU;oBACZU,aAAaA,WAAWmB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAE9B,UAAU,GAAG,OAAO;gBACrE;gBAEA,IAAID,MAAM;oBACR,MAAMgC,SAAS5F,oBAAoBuE,YAAYX,KAAKiC,OAAO;oBAC3DtB,aAAaqB,OAAO9D,QAAQ;oBAC5BuC,UAAU9C,KAAK,CAACuE,kBAAkB,GAChCF,OAAOG,cAAc,IAAIhE,OAAO+D,kBAAkB;gBACtD;gBAEA,IAAIvB,eAAeZ,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAII,iBAAiBG,qBAAqB;oBACxC,MAAM8B,gBAAgB9B,oBAAoBK;oBAC1C,IAAIyB,eAAe;wBACjB3B,UAAU9C,KAAK,GAAG;4BAChB,GAAG8C,UAAU9C,KAAK;4BAClB,GAAGyE,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMrB,WAAWb,SAASmC,WAAW,IAAI,EAAE,CAAE;YAChDvB,aAAaC;QACf;QAEA,IAAIJ,eAAeZ,MAAM;YACvB,IAAIuC,WAAW;YAEf,KAAK,MAAMvB,WAAWb,SAASqC,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWxB,aAAaC;gBACxB,IAAIuB,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC1B,eAAe;gBAC/B,KAAK,MAAMG,WAAWb,SAASsC,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWxB,aAAaC;oBACxB,IAAIuB,UAAU;gBAChB;YACF;QACF;QACA,OAAO5B;IACT;IAEA,SAAS+B,0BAA0BC,kBAA0B;QAC3D,yEAAyE;QACzE,UAAU;QACV,IAAI,CAACtF,mBAAmB,OAAO;QAE/B,MAAM,EAAEY,MAAM,EAAE2E,SAAS,EAAE,GAAGvF;QAE9B,MAAM4D,UAAUzE,gBAAgB;YAC9BqG,IAAI;gBACF,qDAAqD;gBACrDC,MAAM,CAACC;oBACL,2CAA2C;oBAC3C,MAAMC,MAA8BtF,OAAOuF,WAAW,CACpD,IAAIC,gBAAgBH;oBAEtB,KAAK,MAAM,CAACtF,KAAKiB,MAAM,IAAIhB,OAAOyF,OAAO,CAACH,KAAM;wBAC9C,MAAMI,gBAAgBnG,wBAAwBQ;wBAC9C,IAAI,CAAC2F,eAAe;wBAEpBJ,GAAG,CAACI,cAAc,GAAG1E;wBACrB,OAAOsE,GAAG,CAACvF,IAAI;oBACjB;oBAEA,gCAAgC;oBAChC,MAAMwE,SAAS,CAAC;oBAChB,KAAK,MAAMoB,WAAW3F,OAAOC,IAAI,CAACiF,WAAY;wBAC5C,MAAMU,YAAYV,SAAS,CAACS,QAAQ;wBAEpC,kEAAkE;wBAClE,IAAI,CAACC,WAAW;wBAEhB,MAAMC,QAAQtF,MAAM,CAACqF,UAAU;wBAC/B,MAAM5E,QAAQsE,GAAG,CAACK,QAAQ;wBAE1B,iEAAiE;wBACjE,IAAI,CAACE,MAAMjF,QAAQ,IAAI,CAACI,OAAO,OAAO;wBAEtCuD,MAAM,CAACsB,MAAMC,GAAG,CAAC,GAAG9E;oBACtB;oBAEA,OAAOuD;gBACT;YACF;YACAhE;QACF;QAEA,MAAMwF,eAAexC,QAAQ0B;QAC7B,IAAI,CAACc,cAAc,OAAO;QAE1B,OAAOA;IACT;IAEA,OAAO;QACLhD;QACApD;QACAkD;QACApB;QACAuD;QACA;;;;;;KAMC,GACDxD,6BAA6B,CAC3BtB,OACAwB;YAEA,IAAI,CAAC/B,qBAAqB,CAAC8B,qBAAqB;gBAC9C,OAAO;oBAAEf,QAAQ,CAAC;oBAAGiB,gBAAgB;gBAAM;YAC7C;YAEA,OAAOH,4BACLtB,OACAP,mBACA8B,qBACAC;QAEJ;QACAlC,oBAAoB,CAACC,KAAsBC,YACzCF,mBAAmBC,KAAKC,WAAWC;QACrCa,wBAAwB,CACtBC,UACAC,SACGF,uBAAuBC,UAAUC,QAAQf;IAChD;AACF;AAEA,OAAO,SAASqG,6BACdC,OAA4B,EAC5BC,aAAiC;IAEjC,OAAO,OAAOD,OAAO,CAAC7G,mCAAmC,KAAK,YAC5D6G,OAAO,CAAC9G,uCAAuC,KAAK+G,gBAClDD,OAAO,CAAC7G,mCAAmC,CAACgD,KAAK,CAAC,OAClD,EAAE;AACR"}