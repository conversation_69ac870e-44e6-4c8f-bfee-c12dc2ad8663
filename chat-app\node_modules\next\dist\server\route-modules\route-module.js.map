{"version": 3, "sources": ["../../../src/server/route-modules/route-module.ts"], "sourcesContent": ["import type { RouteDefinition } from '../route-definitions/route-definition'\n\n/**\n * RouteModuleOptions is the options that are passed to the route module, other\n * route modules should extend this class to add specific options for their\n * route.\n */\nexport interface RouteModuleOptions<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  readonly definition: Readonly<D>\n  readonly userland: Readonly<U>\n}\n\n/**\n * RouteHandlerContext is the base context for a route handler.\n */\nexport interface RouteModuleHandleContext {\n  /**\n   * Any matched parameters for the request. This is only defined for dynamic\n   * routes.\n   */\n  params: Record<string, string | string[] | undefined> | undefined\n}\n\n/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */\nexport abstract class RouteModule<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  /**\n   * The userland module. This is the module that is exported from the user's\n   * code. This is marked as readonly to ensure that the module is not mutated\n   * because the module (when compiled) only provides getters.\n   */\n  public readonly userland: Readonly<U>\n\n  /**\n   * The definition of the route.\n   */\n  public readonly definition: Readonly<D>\n\n  /**\n   * The shared modules that are exposed and required for the route module.\n   */\n  public static readonly sharedModules: any\n\n  constructor({ userland, definition }: RouteModuleOptions<D, U>) {\n    this.userland = userland\n    this.definition = definition\n  }\n}\n"], "names": ["RouteModule", "constructor", "userland", "definition"], "mappings": ";;;;+BA8BsBA;;;eAAAA;;;AAAf,MAAeA;IAqBpBC,YAAY,EAAEC,QAAQ,EAAEC,UAAU,EAA4B,CAAE;QAC9D,IAAI,CAACD,QAAQ,GAAGA;QAChB,IAAI,CAACC,UAAU,GAAGA;IACpB;AACF"}