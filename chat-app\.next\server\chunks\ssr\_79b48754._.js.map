{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-red-600 text-white hover:bg-red-700\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,0CAA0C,YAAY;YACtD,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/about/page.tsx"], "sourcesContent": ["import Navigation from \"@/components/layout/Navigation\"\nimport { <PERSON><PERSON> } from \"@/components/ui/Button\"\nimport Link from \"next/link\"\nimport { Target, Heart, Lightbulb, Globe } from \"lucide-react\"\n\nexport default function About() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navigation />\n      \n      {/* Hero Section */}\n      <section className=\"relative py-24 bg-gradient-to-br from-indigo-50 to-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\">\n              About Our Mission\n            </h1>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\">\n              We're building the future of human-AI interaction, creating meaningful \n              conversations that enhance productivity and creativity while maintaining \n              the highest standards of privacy and security.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Mission & Values */}\n      <section className=\"py-24\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n            <div>\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n                Our Story\n              </h2>\n              <p className=\"mt-6 text-lg text-gray-600\">\n                Founded with the vision of democratizing access to intelligent conversation, \n                our platform bridges the gap between human creativity and artificial intelligence. \n                We believe that technology should enhance human potential, not replace it.\n              </p>\n              <p className=\"mt-4 text-lg text-gray-600\">\n                Every conversation on our platform is designed to be secure, private, and \n                meaningful. We've built our infrastructure from the ground up with privacy \n                by design, ensuring that your data remains yours.\n              </p>\n            </div>\n            <div className=\"grid grid-cols-2 gap-6\">\n              <div className=\"text-center p-6 bg-indigo-50 rounded-lg\">\n                <Target className=\"h-12 w-12 text-indigo-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Our Mission</h3>\n                <p className=\"mt-2 text-gray-600\">\n                  Empowering meaningful conversations through intelligent technology\n                </p>\n              </div>\n              <div className=\"text-center p-6 bg-indigo-50 rounded-lg\">\n                <Heart className=\"h-12 w-12 text-indigo-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Our Values</h3>\n                <p className=\"mt-2 text-gray-600\">\n                  Privacy, security, and user-centric design in everything we do\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Deep Dive */}\n      <section className=\"py-24 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              What Makes Us Different\n            </h2>\n            <p className=\"mt-4 text-lg text-gray-600\">\n              Built with cutting-edge technology and user experience in mind\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12\">\n            <div className=\"flex items-start space-x-4\">\n              <div className=\"flex-shrink-0\">\n                <Lightbulb className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                  Advanced AI Integration\n                </h3>\n                <p className=\"text-gray-600\">\n                  Our platform leverages state-of-the-art AI models to provide \n                  intelligent, context-aware responses that feel natural and helpful. \n                  Every interaction is designed to be productive and engaging.\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-4\">\n              <div className=\"flex-shrink-0\">\n                <Globe className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                  Global Accessibility\n                </h3>\n                <p className=\"text-gray-600\">\n                  Built for users worldwide with responsive design, fast loading times, \n                  and intuitive interfaces that work seamlessly across all devices and \n                  connection speeds.\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-4\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">DB</span>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                  Secure Data Management\n                </h3>\n                <p className=\"text-gray-600\">\n                  Powered by Neon PostgreSQL for reliable, scalable data storage. \n                  Your conversation history is encrypted and stored securely, \n                  accessible only to you.\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-4\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">∞</span>\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                  Continuous Innovation\n                </h3>\n                <p className=\"text-gray-600\">\n                  We're constantly improving our platform with new features, \n                  better performance, and enhanced user experience based on \n                  community feedback and technological advances.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Team Section */}\n      <section className=\"py-24\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              Built by Innovators\n            </h2>\n            <p className=\"mt-4 text-lg text-gray-600 max-w-2xl mx-auto\">\n              Our team combines expertise in AI, security, and user experience \n              to create a platform that's both powerful and accessible.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-indigo-600\">\n        <div className=\"max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n              Ready to Experience the Future?\n            </h2>\n            <p className=\"mt-4 text-lg text-indigo-200\">\n              Join our community and start having meaningful conversations today\n            </p>\n            <div className=\"mt-8 flex justify-center gap-4\">\n              <Link href=\"/auth/signup\">\n                <Button \n                  size=\"lg\" \n                  className=\"bg-white text-indigo-600 hover:bg-gray-50 text-lg px-8 py-3\"\n                >\n                  Get Started\n                </Button>\n              </Link>\n              <Link href=\"/\">\n                <Button \n                  variant=\"outline\" \n                  size=\"lg\" \n                  className=\"border-white text-white hover:bg-white hover:text-indigo-600 text-lg px-8 py-3\"\n                >\n                  Back to Home\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAAyD;;;;;;;;;;;;;;;;;;;;;;0BAU5E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAK1C,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAIpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DAGzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAQjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DAGzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAQjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;sDAGnD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DAGzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAQjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;sDAGnD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DAGzD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;0BASlE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}