{"version": 3, "sources": ["../../../src/server/app-render/render-css-resource.tsx"], "sourcesContent": ["import type { CssResource } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport type { AppRenderContext } from './app-render'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport type { PreloadCallbacks } from './types'\n\n/**\n * Abstracts the rendering of CSS files based on whether they are inlined or not.\n * For inlined CSS, renders a <style> tag with the CSS content directly embedded.\n * For external CSS files, renders a <link> tag pointing to the CSS file.\n */\nexport function renderCssResource(\n  entryCssFiles: CssResource[],\n  ctx: AppRenderContext,\n  preloadCallbacks?: PreloadCallbacks\n) {\n  return entryCssFiles.map((entryCssFile, index) => {\n    // `Precedence` is an opt-in signal for React to handle resource\n    // loading and deduplication, etc. It's also used as the key to sort\n    // resources so they will be injected in the correct order.\n    // During HMR, it's critical to use different `precedence` values\n    // for different stylesheets, so their order will be kept.\n    // https://github.com/facebook/react/pull/25060\n    const precedence =\n      process.env.NODE_ENV === 'development'\n        ? 'next_' + entryCssFile.path\n        : 'next'\n\n    // In dev, Safari and Firefox will cache the resource during HMR:\n    // - https://github.com/vercel/next.js/issues/5860\n    // - https://bugs.webkit.org/show_bug.cgi?id=187726\n    // Because of this, we add a `?v=` query to bypass the cache during\n    // development. We need to also make sure that the number is always\n    // increasing.\n    const fullHref = `${ctx.assetPrefix}/_next/${encodeURIPath(\n      entryCssFile.path\n    )}${getAssetQueryString(ctx, true)}`\n\n    if (entryCssFile.inlined && !ctx.parsedRequestHeaders.isRSCRequest) {\n      return (\n        <style\n          key={index}\n          nonce={ctx.nonce}\n          // @ts-ignore\n          precedence={precedence}\n          href={fullHref}\n        >\n          {entryCssFile.content}\n        </style>\n      )\n    }\n\n    preloadCallbacks?.push(() => {\n      ctx.componentMod.preloadStyle(\n        fullHref,\n        ctx.renderOpts.crossOrigin,\n        ctx.nonce\n      )\n    })\n\n    return (\n      <link\n        key={index}\n        rel=\"stylesheet\"\n        href={fullHref}\n        // @ts-ignore\n        precedence={precedence}\n        crossOrigin={ctx.renderOpts.crossOrigin}\n        nonce={ctx.nonce}\n      />\n    )\n  })\n}\n"], "names": ["encodeURIPath", "getAssetQueryString", "renderCssResource", "entryCssFiles", "ctx", "preloadCallbacks", "map", "entryCssFile", "index", "precedence", "process", "env", "NODE_ENV", "path", "fullHref", "assetPrefix", "inlined", "parsedRequestHeaders", "isRSCRequest", "style", "nonce", "href", "content", "push", "componentMod", "preloadStyle", "renderOpts", "crossOrigin", "link", "rel"], "mappings": ";AACA,SAASA,aAAa,QAAQ,mCAAkC;AAEhE,SAASC,mBAAmB,QAAQ,2BAA0B;AAG9D;;;;CAIC,GACD,OAAO,SAASC,kBACdC,aAA4B,EAC5BC,GAAqB,EACrBC,gBAAmC;IAEnC,OAAOF,cAAcG,GAAG,CAAC,CAACC,cAAcC;QACtC,gEAAgE;QAChE,oEAAoE;QACpE,2DAA2D;QAC3D,iEAAiE;QACjE,0DAA0D;QAC1D,+CAA+C;QAC/C,MAAMC,aACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrB,UAAUL,aAAaM,IAAI,GAC3B;QAEN,iEAAiE;QACjE,kDAAkD;QAClD,mDAAmD;QACnD,mEAAmE;QACnE,mEAAmE;QACnE,cAAc;QACd,MAAMC,WAAW,GAAGV,IAAIW,WAAW,CAAC,OAAO,EAAEf,cAC3CO,aAAaM,IAAI,IACfZ,oBAAoBG,KAAK,OAAO;QAEpC,IAAIG,aAAaS,OAAO,IAAI,CAACZ,IAAIa,oBAAoB,CAACC,YAAY,EAAE;YAClE,qBACE,KAACC;gBAECC,OAAOhB,IAAIgB,KAAK;gBAChB,aAAa;gBACbX,YAAYA;gBACZY,MAAMP;0BAELP,aAAae,OAAO;eANhBd;QASX;QAEAH,oCAAAA,iBAAkBkB,IAAI,CAAC;YACrBnB,IAAIoB,YAAY,CAACC,YAAY,CAC3BX,UACAV,IAAIsB,UAAU,CAACC,WAAW,EAC1BvB,IAAIgB,KAAK;QAEb;QAEA,qBACE,KAACQ;YAECC,KAAI;YACJR,MAAMP;YACN,aAAa;YACbL,YAAYA;YACZkB,aAAavB,IAAIsB,UAAU,CAACC,WAAW;YACvCP,OAAOhB,IAAIgB,KAAK;WANXZ;IASX;AACF"}