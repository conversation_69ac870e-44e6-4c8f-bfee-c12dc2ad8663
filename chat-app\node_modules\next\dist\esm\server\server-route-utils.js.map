{"version": 3, "sources": ["../../src/server/server-route-utils.ts"], "sourcesContent": ["import type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\n\nimport { getRequestMeta } from './request-meta'\nimport { stringify as stringifyQs } from 'querystring'\n\n// since initial query values are decoded by querystring.parse\n// we need to re-encode them here but still allow passing through\n// values from rewrites/redirects\nexport const stringifyQuery = (req: BaseNextRequest, query: ParsedUrlQuery) => {\n  const initialQuery = getRequestMeta(req, 'initQuery') || {}\n  const initialQueryValues = Object.values(initialQuery)\n\n  return stringifyQs(query, undefined, undefined, {\n    encodeURIComponent(value) {\n      if (\n        value in initialQuery ||\n        initialQueryValues.some((initialQueryVal) => {\n          // `value` always refers to a query value, even if it's nested in an array\n          return Array.isArray(initialQueryVal)\n            ? initialQueryVal.includes(value)\n            : initialQueryVal === value\n        })\n      ) {\n        // Encode keys and values from initial query\n        return encodeURIComponent(value)\n      }\n\n      return value\n    },\n  })\n}\n"], "names": ["getRequestMeta", "stringify", "stringifyQs", "stringifyQuery", "req", "query", "initialQuery", "initialQueryValues", "Object", "values", "undefined", "encodeURIComponent", "value", "some", "initialQueryVal", "Array", "isArray", "includes"], "mappings": "AAGA,SAASA,cAAc,QAAQ,iBAAgB;AAC/C,SAASC,aAAaC,WAAW,QAAQ,cAAa;AAEtD,8DAA8D;AAC9D,iEAAiE;AACjE,iCAAiC;AACjC,OAAO,MAAMC,iBAAiB,CAACC,KAAsBC;IACnD,MAAMC,eAAeN,eAAeI,KAAK,gBAAgB,CAAC;IAC1D,MAAMG,qBAAqBC,OAAOC,MAAM,CAACH;IAEzC,OAAOJ,YAAYG,OAAOK,WAAWA,WAAW;QAC9CC,oBAAmBC,KAAK;YACtB,IACEA,SAASN,gBACTC,mBAAmBM,IAAI,CAAC,CAACC;gBACvB,0EAA0E;gBAC1E,OAAOC,MAAMC,OAAO,CAACF,mBACjBA,gBAAgBG,QAAQ,CAACL,SACzBE,oBAAoBF;YAC1B,IACA;gBACA,4CAA4C;gBAC5C,OAAOD,mBAAmBC;YAC5B;YAEA,OAAOA;QACT;IACF;AACF,EAAC"}