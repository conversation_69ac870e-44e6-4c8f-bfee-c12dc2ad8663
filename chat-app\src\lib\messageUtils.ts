/**
 * Utility functions for processing chat messages and API responses
 */

/**
 * Extracts clean text from various API response formats
 */
export function extractMessageContent(apiResponse: any): string {
  if (!apiResponse) {
    return "I received your message but couldn't generate a response. Please try again."
  }

  // If it's already a string, return it
  if (typeof apiResponse === 'string') {
    return apiResponse.trim()
  }

  // If it's an object, try to extract text from common properties
  if (typeof apiResponse === 'object') {
    const possibleTextFields = [
      'text',
      'response', 
      'answer',
      'message',
      'content',
      'result',
      'output',
      'reply',
      'data'
    ]

    for (const field of possibleTextFields) {
      if (apiResponse[field] && typeof apiResponse[field] === 'string') {
        return apiResponse[field].trim()
      }
    }

    // If no text field found, try to stringify but make it readable
    try {
      const stringified = JSON.stringify(apiResponse, null, 2)
      // If the JSON is too long or complex, provide a fallback
      if (stringified.length > 500) {
        return "I received a complex response. Please try rephrasing your question."
      }
      return stringified
    } catch (error) {
      return "I received your message but couldn't process the response format."
    }
  }

  return "I received your message but couldn't generate a proper response. Please try again."
}

/**
 * Formats message content for display
 */
export function formatMessageForDisplay(content: string): string {
  if (!content) return ""
  
  // Clean up any extra whitespace
  return content.trim()
}

/**
 * Truncates long messages for chat session titles
 */
export function createChatTitle(message: string, maxLength: number = 50): string {
  if (!message) return "New Chat"
  
  const cleaned = message.trim()
  if (cleaned.length <= maxLength) return cleaned
  
  return cleaned.substring(0, maxLength) + "..."
}
