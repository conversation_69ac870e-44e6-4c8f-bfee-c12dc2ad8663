{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON>rovider from \"next-auth/providers/credentials\"\nimport { PrismaAdapter } from \"@auth/prisma-adapter\"\nimport { prisma } from \"./prisma\"\nimport bcrypt from \"bcryptjs\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.password) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: \"jwt\"\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n    signUp: \"/auth/signup\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n      }\n      return session\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAC5B;YACA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/messageUtils.ts"], "sourcesContent": ["/**\n * Utility functions for processing chat messages and API responses\n */\n\n/**\n * Extracts clean text from various API response formats\n */\nexport function extractMessageContent(apiResponse: any): string {\n  if (!apiResponse) {\n    return \"I received your message but couldn't generate a response. Please try again.\"\n  }\n\n  // If it's already a string, return it\n  if (typeof apiResponse === 'string') {\n    return apiResponse.trim()\n  }\n\n  // If it's an object, try to extract text from common properties\n  if (typeof apiResponse === 'object') {\n    const possibleTextFields = [\n      'text',\n      'response', \n      'answer',\n      'message',\n      'content',\n      'result',\n      'output',\n      'reply',\n      'data'\n    ]\n\n    for (const field of possibleTextFields) {\n      if (apiResponse[field] && typeof apiResponse[field] === 'string') {\n        return apiResponse[field].trim()\n      }\n    }\n\n    // If no text field found, try to stringify but make it readable\n    try {\n      const stringified = JSON.stringify(apiResponse, null, 2)\n      // If the JSON is too long or complex, provide a fallback\n      if (stringified.length > 500) {\n        return \"I received a complex response. Please try rephrasing your question.\"\n      }\n      return stringified\n    } catch (error) {\n      return \"I received your message but couldn't process the response format.\"\n    }\n  }\n\n  return \"I received your message but couldn't generate a proper response. Please try again.\"\n}\n\n/**\n * Formats message content for display\n */\nexport function formatMessageForDisplay(content: string): string {\n  if (!content) return \"\"\n  \n  // Clean up any extra whitespace\n  return content.trim()\n}\n\n/**\n * Truncates long messages for chat session titles\n */\nexport function createChatTitle(message: string, maxLength: number = 50): string {\n  if (!message) return \"New Chat\"\n  \n  const cleaned = message.trim()\n  if (cleaned.length <= maxLength) return cleaned\n  \n  return cleaned.substring(0, maxLength) + \"...\"\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;AACM,SAAS,sBAAsB,WAAgB;IACpD,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,sCAAsC;IACtC,IAAI,OAAO,gBAAgB,UAAU;QACnC,OAAO,YAAY,IAAI;IACzB;IAEA,gEAAgE;IAChE,IAAI,OAAO,gBAAgB,UAAU;QACnC,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,SAAS,mBAAoB;YACtC,IAAI,WAAW,CAAC,MAAM,IAAI,OAAO,WAAW,CAAC,MAAM,KAAK,UAAU;gBAChE,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI;YAChC;QACF;QAEA,gEAAgE;QAChE,IAAI;YACF,MAAM,cAAc,KAAK,SAAS,CAAC,aAAa,MAAM;YACtD,yDAAyD;YACzD,IAAI,YAAY,MAAM,GAAG,KAAK;gBAC5B,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAKO,SAAS,wBAAwB,OAAe;IACrD,IAAI,CAAC,SAAS,OAAO;IAErB,gCAAgC;IAChC,OAAO,QAAQ,IAAI;AACrB;AAKO,SAAS,gBAAgB,OAAe,EAAE,YAAoB,EAAE;IACrE,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,UAAU,QAAQ,IAAI;IAC5B,IAAI,QAAQ,MAAM,IAAI,WAAW,OAAO;IAExC,OAAO,QAAQ,SAAS,CAAC,GAAG,aAAa;AAC3C", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\"\nimport { getServerSession } from \"next-auth\"\nimport { authOptions } from \"@/lib/auth\"\nimport { prisma } from \"@/lib/prisma\"\nimport { extractMessageContent, createChatTitle } from \"@/lib/messageUtils\"\nimport axios from \"axios\"\n\nconst CHAT_API_URL = process.env.CHAT_API_URL || \"https://studio.cobraaisystems.com/api/v1/prediction/149d3377-d718-4d99-8f06-6441987bf400\"\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: \"Unauthorized\" },\n        { status: 401 }\n      )\n    }\n\n    const { message, chatSessionId } = await request.json()\n\n    if (!message) {\n      return NextResponse.json(\n        { error: \"Message is required\" },\n        { status: 400 }\n      )\n    }\n\n    let sessionId = chatSessionId\n\n    // Create new chat session if none provided\n    if (!sessionId) {\n      const newSession = await prisma.chatSession.create({\n        data: {\n          userId: session.user.id,\n          title: createChatTitle(message)\n        }\n      })\n      sessionId = newSession.id\n    }\n\n    // Save user message\n    await prisma.chatMessage.create({\n      data: {\n        chatSessionId: sessionId,\n        content: message,\n        role: \"user\"\n      }\n    })\n\n    // Call external API\n    const response = await axios.post(CHAT_API_URL, {\n      question: message\n    })\n\n    const apiResponse = response.data\n\n    // Extract clean text from the API response\n    const assistantMessage = extractMessageContent(apiResponse)\n\n    // Save assistant response (clean text only)\n    await prisma.chatMessage.create({\n      data: {\n        chatSessionId: sessionId,\n        content: assistantMessage,\n        role: \"assistant\"\n      }\n    })\n\n    return NextResponse.json({\n      message: assistantMessage,\n      chatSessionId: sessionId\n    })\n\n  } catch (error) {\n    console.error(\"Chat API error:\", error)\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: \"Unauthorized\" },\n        { status: 401 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const chatSessionId = searchParams.get(\"sessionId\")\n\n    if (!chatSessionId) {\n      // Get all chat sessions for user\n      const chatSessions = await prisma.chatSession.findMany({\n        where: { userId: session.user.id },\n        orderBy: { updatedAt: \"desc\" },\n        include: {\n          messages: {\n            orderBy: { createdAt: \"asc\" }\n          }\n        }\n      })\n\n      return NextResponse.json({ chatSessions })\n    } else {\n      // Get specific chat session with messages\n      const chatSession = await prisma.chatSession.findFirst({\n        where: {\n          id: chatSessionId,\n          userId: session.user.id\n        },\n        include: {\n          messages: {\n            orderBy: { createdAt: \"asc\" }\n          }\n        }\n      })\n\n      if (!chatSession) {\n        return NextResponse.json(\n          { error: \"Chat session not found\" },\n          { status: 404 }\n        )\n      }\n\n      return NextResponse.json({ chatSession })\n    }\n\n  } catch (error) {\n    console.error(\"Chat GET API error:\", error)\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY,IAAI;AAE1C,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,IAAI;QAErD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,YAAY;QAEhB,2CAA2C;QAC3C,IAAI,CAAC,WAAW;YACd,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBACjD,MAAM;oBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,OAAO,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE;gBACzB;YACF;YACA,YAAY,WAAW,EAAE;QAC3B;QAEA,oBAAoB;QACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,eAAe;gBACf,SAAS;gBACT,MAAM;YACR;QACF;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,cAAc;YAC9C,UAAU;QACZ;QAEA,MAAM,cAAc,SAAS,IAAI;QAEjC,2CAA2C;QAC3C,MAAM,mBAAmB,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;QAE/C,4CAA4C;QAC5C,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,eAAe;gBACf,SAAS;gBACT,MAAM;YACR;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,eAAe;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC;QAEvC,IAAI,CAAC,eAAe;YAClB,iCAAiC;YACjC,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACrD,OAAO;oBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBAAC;gBACjC,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,SAAS;oBACP,UAAU;wBACR,SAAS;4BAAE,WAAW;wBAAM;oBAC9B;gBACF;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAa;QAC1C,OAAO;YACL,0CAA0C;YAC1C,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBACrD,OAAO;oBACL,IAAI;oBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACzB;gBACA,SAAS;oBACP,UAAU;wBACR,SAAS;4BAAE,WAAW;wBAAM;oBAC9B;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyB,GAClC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAY;QACzC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}