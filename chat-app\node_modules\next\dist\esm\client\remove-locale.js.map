{"version": 3, "sources": ["../../src/client/remove-locale.ts"], "sourcesContent": ["import { parsePath } from '../shared/lib/router/utils/parse-path'\n\nexport function removeLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { pathname } = parsePath(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale?.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith(`/${localeLower}/`) ||\n        pathLower === `/${localeLower}`)\n      ? `${pathname.length === locale.length + 1 ? `/` : ``}${path.slice(\n          locale.length + 1\n        )}`\n      : path\n  }\n  return path\n}\n"], "names": ["parsePath", "removeLocale", "path", "locale", "process", "env", "__NEXT_I18N_SUPPORT", "pathname", "pathLower", "toLowerCase", "localeLower", "startsWith", "length", "slice"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wCAAuC;AAEjE,OAAO,SAASC,aAAaC,IAAY,EAAEC,MAAe;IACxD,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;QACnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,UAAUE;QAC/B,MAAMM,YAAYD,SAASE,WAAW;QACtC,MAAMC,cAAcP,0BAAAA,OAAQM,WAAW;QAEvC,OAAON,UACJK,CAAAA,UAAUG,UAAU,CAAC,AAAC,MAAGD,cAAY,QACpCF,cAAc,AAAC,MAAGE,WAAY,IAC9B,AAAC,KAAEH,CAAAA,SAASK,MAAM,KAAKT,OAAOS,MAAM,GAAG,IAAK,MAAM,EAAA,IAAIV,KAAKW,KAAK,CAC9DV,OAAOS,MAAM,GAAG,KAElBV;IACN;IACA,OAAOA;AACT"}