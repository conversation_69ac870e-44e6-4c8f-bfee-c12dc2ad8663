import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { extractMessageContent, createChatTitle } from "@/lib/messageUtils"
import axios from "axios"

const CHAT_API_URL = process.env.CHAT_API_URL || "https://studio.cobraaisystems.com/api/v1/prediction/149d3377-d718-4d99-8f06-6441987bf400"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { message, chatSessionId } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 }
      )
    }

    let sessionId = chatSessionId

    // Create new chat session if none provided
    if (!sessionId) {
      const newSession = await prisma.chatSession.create({
        data: {
          userId: session.user.id,
          title: createChatTitle(message)
        }
      })
      sessionId = newSession.id
    }

    // Save user message
    await prisma.chatMessage.create({
      data: {
        chatSessionId: sessionId,
        content: message,
        role: "user"
      }
    })

    // Call external API
    const response = await axios.post(CHAT_API_URL, {
      question: message
    })

    const apiResponse = response.data

    // Extract clean text from the API response
    const assistantMessage = extractMessageContent(apiResponse)

    // Save assistant response (clean text only)
    await prisma.chatMessage.create({
      data: {
        chatSessionId: sessionId,
        content: assistantMessage,
        role: "assistant"
      }
    })

    return NextResponse.json({
      message: assistantMessage,
      chatSessionId: sessionId
    })

  } catch (error) {
    console.error("Chat API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const chatSessionId = searchParams.get("sessionId")

    if (!chatSessionId) {
      // Get all chat sessions for user
      const chatSessions = await prisma.chatSession.findMany({
        where: { userId: session.user.id },
        orderBy: { updatedAt: "desc" },
        include: {
          messages: {
            orderBy: { createdAt: "asc" }
          }
        }
      })

      return NextResponse.json({ chatSessions })
    } else {
      // Get specific chat session with messages
      const chatSession = await prisma.chatSession.findFirst({
        where: {
          id: chatSessionId,
          userId: session.user.id
        },
        include: {
          messages: {
            orderBy: { createdAt: "asc" }
          }
        }
      })

      if (!chatSession) {
        return NextResponse.json(
          { error: "Chat session not found" },
          { status: 404 }
        )
      }

      return NextResponse.json({ chatSession })
    }

  } catch (error) {
    console.error("Chat GET API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
