{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/hooks/use-delayed-render.ts"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react'\n\ninterface Options {\n  enterDelay?: number\n  exitDelay?: number\n  onUnmount?: () => void\n}\n\n/**\n * Useful to perform CSS transitions on React components without\n * using libraries like Framer Motion. This hook will defer the\n * unmount of a React component until after a delay.\n *\n * @param active - Whether the component should be rendered\n * @param options - Options for the delayed render\n * @param options.enterDelay - Delay before rendering the component\n * @param options.exitDelay - Delay before unmounting the component\n *\n * const Modal = ({ active }) => {\n * const { mounted, rendered } = useDelayedRender(active, {\n *  exitDelay: 2000,\n * })\n *\n * if (!mounted) return null\n *\n * return (\n *   <Portal>\n *     <div className={rendered ? 'modal visible' : 'modal'}>...</div>\n *   </Portal>\n * )\n *}\n *\n * */\nexport function useDelayedRender(active = false, options: Options = {}) {\n  const [mounted, setMounted] = useState(active)\n  const [rendered, setRendered] = useState(false)\n  const renderTimerRef = useRef<number | null>(null)\n  const unmountTimerRef = useRef<number | null>(null)\n\n  const clearTimers = useCallback(() => {\n    if (renderTimerRef.current !== null) {\n      window.clearTimeout(renderTimerRef.current)\n      renderTimerRef.current = null\n    }\n    if (unmountTimerRef.current !== null) {\n      window.clearTimeout(unmountTimerRef.current)\n      unmountTimerRef.current = null\n    }\n  }, [])\n\n  useEffect(() => {\n    const { enterDelay = 1, exitDelay = 0 } = options\n\n    clearTimers()\n\n    if (active) {\n      setMounted(true)\n      if (enterDelay <= 0) {\n        setRendered(true)\n      } else {\n        renderTimerRef.current = window.setTimeout(() => {\n          setRendered(true)\n        }, enterDelay)\n      }\n    } else {\n      setRendered(false)\n      if (exitDelay <= 0) {\n        setMounted(false)\n      } else {\n        unmountTimerRef.current = window.setTimeout(() => {\n          setMounted(false)\n        }, exitDelay)\n      }\n    }\n\n    return clearTimers\n  }, [active, options, clearTimers])\n\n  return { mounted, rendered }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "active", "options", "mounted", "setMounted", "useState", "rendered", "setRendered", "renderTimerRef", "useRef", "unmountTimerRef", "clearTimers", "useCallback", "current", "window", "clearTimeout", "useEffect", "enterDelay", "exitDelay", "setTimeout"], "mappings": ";;;;+BAiCgBA;;;eAAAA;;;uBAjCyC;AAiClD,SAASA,iBAAiBC,MAAc,EAAEC,OAAqB;IAArCD,IAAAA,mBAAAA,SAAS;IAAOC,IAAAA,oBAAAA,UAAmB,CAAC;IACnE,MAAM,CAACC,SAASC,WAAW,GAAGC,IAAAA,eAAQ,EAACJ;IACvC,MAAM,CAACK,UAAUC,YAAY,GAAGF,IAAAA,eAAQ,EAAC;IACzC,MAAMG,iBAAiBC,IAAAA,aAAM,EAAgB;IAC7C,MAAMC,kBAAkBD,IAAAA,aAAM,EAAgB;IAE9C,MAAME,cAAcC,IAAAA,kBAAW,EAAC;QAC9B,IAAIJ,eAAeK,OAAO,KAAK,MAAM;YACnCC,OAAOC,YAAY,CAACP,eAAeK,OAAO;YAC1CL,eAAeK,OAAO,GAAG;QAC3B;QACA,IAAIH,gBAAgBG,OAAO,KAAK,MAAM;YACpCC,OAAOC,YAAY,CAACL,gBAAgBG,OAAO;YAC3CH,gBAAgBG,OAAO,GAAG;QAC5B;IACF,GAAG,EAAE;IAELG,IAAAA,gBAAS,EAAC;QACR,MAAM,EAAEC,aAAa,CAAC,EAAEC,YAAY,CAAC,EAAE,GAAGhB;QAE1CS;QAEA,IAAIV,QAAQ;YACVG,WAAW;YACX,IAAIa,cAAc,GAAG;gBACnBV,YAAY;YACd,OAAO;gBACLC,eAAeK,OAAO,GAAGC,OAAOK,UAAU,CAAC;oBACzCZ,YAAY;gBACd,GAAGU;YACL;QACF,OAAO;YACLV,YAAY;YACZ,IAAIW,aAAa,GAAG;gBAClBd,WAAW;YACb,OAAO;gBACLM,gBAAgBG,OAAO,GAAGC,OAAOK,UAAU,CAAC;oBAC1Cf,WAAW;gBACb,GAAGc;YACL;QACF;QAEA,OAAOP;IACT,GAAG;QAACV;QAAQC;QAASS;KAAY;IAEjC,OAAO;QAAER;QAASG;IAAS;AAC7B"}