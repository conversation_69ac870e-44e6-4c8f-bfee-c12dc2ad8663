{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/auth/signup/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport Link from \"next/link\"\n\nexport default function SignUp() {\n  const [name, setName] = useState(\"\")\n  const [email, setEmail] = useState(\"\")\n  const [password, setPassword] = useState(\"\")\n  const [confirmPassword, setConfirmPassword] = useState(\"\")\n  const [error, setError] = useState(\"\")\n  const [loading, setLoading] = useState(false)\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError(\"\")\n\n    if (password !== confirmPassword) {\n      setError(\"Passwords do not match\")\n      setLoading(false)\n      return\n    }\n\n    if (password.length < 6) {\n      setError(\"Password must be at least 6 characters\")\n      setLoading(false)\n      return\n    }\n\n    try {\n      const response = await fetch(\"/api/auth/register\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          name,\n          email,\n          password\n        })\n      })\n\n      if (response.ok) {\n        router.push(\"/auth/signin?message=Account created successfully\")\n      } else {\n        const data = await response.json()\n        setError(data.error || \"An error occurred\")\n      }\n    } catch (error) {\n      setError(\"An error occurred. Please try again.\")\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{\" \"}\n            <Link\n              href=\"/auth/signin\"\n              className=\"font-medium text-indigo-600 hover:text-indigo-500\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"rounded-md shadow-sm -space-y-px\">\n            <div>\n              <label htmlFor=\"name\" className=\"sr-only\">\n                Full name\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                autoComplete=\"name\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Full name\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"email\" className=\"sr-only\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Email address\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"password\" className=\"sr-only\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"sr-only\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Confirm password\"\n                value={confirmPassword}\n                onChange={(e) => setConfirmPassword(e.target.value)}\n              />\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"text-red-600 text-sm text-center\">{error}</div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\"\n            >\n              {loading ? \"Creating account...\" : \"Create account\"}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <Link\n              href=\"/\"\n              className=\"font-medium text-indigo-600 hover:text-indigo-500\"\n            >\n              Back to home\n            </Link>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI,aAAa,iBAAiB;YAChC,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAKL,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAAU;;;;;;sDAG1C,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAG3C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAU;;;;;;sDAG3C,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAG5C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAU;;;;;;sDAG9C,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAG/C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;sDAGrD,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;wBAKvD,uBACC,8OAAC;4BAAI,WAAU;sCAAoC;;;;;;sCAGrD,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,wBAAwB;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}