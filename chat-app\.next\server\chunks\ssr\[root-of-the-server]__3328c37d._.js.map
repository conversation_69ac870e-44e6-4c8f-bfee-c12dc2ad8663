{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-red-600 text-white hover:bg-red-700\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,0CAA0C,YAAY;YACtD,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/components/layout/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession, signOut } from \"next-auth/react\"\nimport Link from \"next/link\"\nimport { Button } from \"@/components/ui/Button\"\n\nexport default function Navigation() {\n  const { data: session, status } = useSession()\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n              ChatApp\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/\"\n              className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              About\n            </Link>\n\n            {status === \"loading\" ? (\n              <div className=\"animate-pulse bg-gray-200 h-8 w-20 rounded\"></div>\n            ) : session ? (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/chat\"\n                  className=\"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Chat\n                </Link>\n                <span className=\"text-sm text-gray-600\">\n                  Welcome, {session.user?.name || session.user?.email}\n                </span>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => signOut()}\n                >\n                  Sign Out\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link href=\"/auth/signin\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    Sign In\n                  </Button>\n                </Link>\n                <Link href=\"/auth/signup\">\n                  <Button size=\"sm\">\n                    Sign Up\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAkC;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAIA,WAAW,0BACV,8OAAC;gCAAI,WAAU;;;;;uCACb,wBACF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAK,WAAU;;4CAAwB;4CAC5B,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI,EAAE;;;;;;;kDAEhD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;kDACtB;;;;;;;;;;;qDAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;sDAAK;;;;;;;;;;;kDAIpC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpC", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/chat/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { redirect } from \"next/navigation\"\nimport Navigation from \"@/components/layout/Navigation\"\nimport { Button } from \"@/components/ui/Button\"\nimport { Send, Plus, MessageCircle, Menu, X, Trash2, MoreVertical } from \"lucide-react\"\n\ninterface Message {\n  id: string\n  content: string\n  role: \"user\" | \"assistant\"\n  createdAt: string\n}\n\ninterface ChatSession {\n  id: string\n  title: string\n  messages: Message[]\n  createdAt: string\n  updatedAt: string\n}\n\nexport default function ChatPage() {\n  const { data: session, status } = useSession()\n  const [messages, setMessages] = useState<Message[]>([])\n  const [inputMessage, setInputMessage] = useState(\"\")\n  const [isLoading, setIsLoading] = useState(false)\n  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)\n  const [chatSessions, setChatSessions] = useState<ChatSession[]>([])\n  const [isSidebarOpen, setIsSidebarOpen] = useState(true)\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)\n  const [showDeleteAllConfirm, setShowDeleteAllConfirm] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      redirect(\"/auth/signin\")\n    }\n  }, [status])\n\n  useEffect(() => {\n    if (session) {\n      loadChatSessions()\n    }\n  }, [session])\n\n  const loadChatSessions = async () => {\n    try {\n      const response = await fetch(\"/api/chat\")\n      if (response.ok) {\n        const data = await response.json()\n        setChatSessions(data.chatSessions || [])\n      }\n    } catch (error) {\n      console.error(\"Failed to load chat sessions:\", error)\n    }\n  }\n\n  const loadChatSession = async (sessionId: string) => {\n    try {\n      const response = await fetch(`/api/chat?sessionId=${sessionId}`)\n      if (response.ok) {\n        const data = await response.json()\n        setMessages(data.chatSession.messages || [])\n        setCurrentSessionId(sessionId)\n      }\n    } catch (error) {\n      console.error(\"Failed to load chat session:\", error)\n    }\n  }\n\n  const startNewChat = () => {\n    setMessages([])\n    setCurrentSessionId(null)\n  }\n\n  const deleteChatSession = async (sessionId: string) => {\n    try {\n      const response = await fetch(`/api/chat/${sessionId}`, {\n        method: \"DELETE\"\n      })\n\n      if (response.ok) {\n        // Remove from local state\n        setChatSessions(prev => prev.filter(session => session.id !== sessionId))\n\n        // If this was the current session, clear it\n        if (currentSessionId === sessionId) {\n          setMessages([])\n          setCurrentSessionId(null)\n        }\n      }\n    } catch (error) {\n      console.error(\"Failed to delete chat session:\", error)\n    }\n    setShowDeleteConfirm(null)\n  }\n\n  const deleteAllChatSessions = async () => {\n    try {\n      const response = await fetch(\"/api/chat/all\", {\n        method: \"DELETE\"\n      })\n\n      if (response.ok) {\n        setChatSessions([])\n        setMessages([])\n        setCurrentSessionId(null)\n      }\n    } catch (error) {\n      console.error(\"Failed to delete all chat sessions:\", error)\n    }\n    setShowDeleteAllConfirm(false)\n  }\n\n  const sendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return\n\n    const userMessage = inputMessage.trim()\n    setInputMessage(\"\")\n    setIsLoading(true)\n\n    // Add user message to UI immediately\n    const tempUserMessage: Message = {\n      id: Date.now().toString(),\n      content: userMessage,\n      role: \"user\",\n      createdAt: new Date().toISOString()\n    }\n    setMessages(prev => [...prev, tempUserMessage])\n\n    try {\n      const response = await fetch(\"/api/chat\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          message: userMessage,\n          chatSessionId: currentSessionId\n        })\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        \n        // Update session ID if this was a new chat\n        if (!currentSessionId) {\n          setCurrentSessionId(data.chatSessionId)\n          loadChatSessions() // Refresh the sessions list\n        }\n\n        // Add assistant response\n        const assistantMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          content: data.message || \"Sorry, I couldn't generate a response.\",\n          role: \"assistant\",\n          createdAt: new Date().toISOString()\n        }\n        setMessages(prev => [...prev, assistantMessage])\n      } else {\n        // Handle error\n        const errorMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          content: \"Sorry, I encountered an error. Please try again.\",\n          role: \"assistant\",\n          createdAt: new Date().toISOString()\n        }\n        setMessages(prev => [...prev, errorMessage])\n      }\n    } catch (error) {\n      console.error(\"Failed to send message:\", error)\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        content: \"Sorry, I encountered an error. Please try again.\",\n        role: \"assistant\",\n        createdAt: new Date().toISOString()\n      }\n      setMessages(prev => [...prev, errorMessage])\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  if (status === \"loading\") {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"flex h-[calc(100vh-4rem)]\">\n        {/* Sidebar */}\n        <div className={`${isSidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 bg-white border-r border-gray-200 flex flex-col overflow-hidden`}>\n          <div className=\"p-4 border-b border-gray-200 flex-shrink-0\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">Chats</h2>\n              <div className=\"flex items-center gap-2\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setShowDeleteAllConfirm(true)}\n                  className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                  disabled={chatSessions.length === 0}\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setIsSidebarOpen(false)}\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n            <Button\n              onClick={startNewChat}\n              className=\"w-full flex items-center gap-2\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              New Chat\n            </Button>\n          </div>\n\n          <div className=\"flex-1 overflow-y-auto p-4\">\n            <h3 className=\"text-sm font-medium text-gray-500 mb-3\">Recent Chats</h3>\n            {chatSessions.length === 0 ? (\n              <p className=\"text-sm text-gray-400\">No chat history yet</p>\n            ) : (\n              <div className=\"space-y-2\">\n                {chatSessions.map((session) => (\n                  <div\n                    key={session.id}\n                    className={`group relative rounded-lg transition-colors ${\n                      currentSessionId === session.id ? \"bg-indigo-50 border border-indigo-200\" : \"hover:bg-gray-50\"\n                    }`}\n                  >\n                    <button\n                      type=\"button\"\n                      onClick={() => loadChatSession(session.id)}\n                      className=\"w-full text-left p-3 rounded-lg text-sm\"\n                    >\n                      <div className=\"flex items-center gap-2 pr-8\">\n                        <MessageCircle className=\"h-4 w-4 text-gray-400 flex-shrink-0\" />\n                        <span className=\"truncate\">\n                          {session.title || \"New Chat\"}\n                        </span>\n                      </div>\n                      <div className=\"text-xs text-gray-400 mt-1\">\n                        {new Date(session.updatedAt).toLocaleDateString()}\n                      </div>\n                    </button>\n\n                    {/* Delete button */}\n                    <button\n                      type=\"button\"\n                      title=\"Delete chat\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        setShowDeleteConfirm(session.id)\n                      }}\n                      className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-red-100 text-red-600\"\n                    >\n                      <Trash2 className=\"h-3 w-3\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Main Chat Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Header with toggle button */}\n          {!isSidebarOpen && (\n            <div className=\"p-4 border-b border-gray-200 bg-white\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsSidebarOpen(true)}\n                className=\"flex items-center gap-2\"\n              >\n                <Menu className=\"h-4 w-4\" />\n                Show Chats\n              </Button>\n            </div>\n          )}\n\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-6\">\n            {messages.length === 0 ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <div className=\"text-center\">\n                  <MessageCircle className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                    Start a conversation\n                  </h3>\n                  <p className=\"text-gray-500\">\n                    Ask me anything and I'll do my best to help!\n                  </p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-6 max-w-4xl mx-auto\">\n                {messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}\n                  >\n                    <div className={`flex items-start space-x-3 ${message.role === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"}`}>\n                      {/* Avatar */}\n                      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                        message.role === \"user\"\n                          ? \"bg-indigo-600 text-white\"\n                          : \"bg-gray-100 text-gray-600\"\n                      }`}>\n                        {message.role === \"user\" ? \"U\" : \"AI\"}\n                      </div>\n\n                      {/* Message bubble */}\n                      <div\n                        className={`max-w-xs lg:max-w-2xl px-4 py-3 rounded-lg shadow-sm ${\n                          message.role === \"user\"\n                            ? \"bg-indigo-600 text-white rounded-br-sm\"\n                            : \"bg-white border border-gray-200 text-gray-900 rounded-bl-sm\"\n                        }`}\n                      >\n                        <p className=\"text-sm leading-relaxed whitespace-pre-wrap break-words\">\n                          {message.content}\n                        </p>\n                        <div className={`text-xs mt-2 ${\n                          message.role === \"user\" ? \"text-indigo-200\" : \"text-gray-500\"\n                        }`}>\n                          {new Date(message.createdAt).toLocaleTimeString([], {\n                            hour: '2-digit',\n                            minute: '2-digit'\n                          })}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n                {isLoading && (\n                  <div className=\"flex justify-start\">\n                    <div className=\"flex items-start space-x-3\">\n                      {/* AI Avatar */}\n                      <div className=\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-gray-100 text-gray-600\">\n                        AI\n                      </div>\n\n                      {/* Typing indicator */}\n                      <div className=\"bg-white border border-gray-200 rounded-lg rounded-bl-sm px-4 py-3 shadow-sm\">\n                        <div className=\"flex space-x-1\">\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:0ms]\"></div>\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:150ms]\"></div>\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:300ms]\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n                <div ref={messagesEndRef} />\n              </div>\n            )}\n          </div>\n\n          {/* Input Area */}\n          <div className=\"border-t border-gray-200 bg-white p-4\">\n            <div className=\"max-w-4xl mx-auto\">\n              <div className=\"flex space-x-4\">\n                <textarea\n                  value={inputMessage}\n                  onChange={(e) => setInputMessage(e.target.value)}\n                  onKeyPress={handleKeyPress}\n                  placeholder=\"Type your message...\"\n                  className=\"flex-1 resize-none border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\n                  rows={1}\n                  disabled={isLoading}\n                />\n                <Button\n                  onClick={sendMessage}\n                  disabled={!inputMessage.trim() || isLoading}\n                  className=\"px-6\"\n                >\n                  <Send className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Delete Single Chat Confirmation Modal */}\n      {showDeleteConfirm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Delete Chat\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Are you sure you want to delete this chat? This action cannot be undone.\n            </p>\n            <div className=\"flex justify-end gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowDeleteConfirm(null)}\n              >\n                Cancel\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={() => deleteChatSession(showDeleteConfirm)}\n                className=\"bg-red-600 hover:bg-red-700 text-white\"\n              >\n                Delete\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete All Chats Confirmation Modal */}\n      {showDeleteAllConfirm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Delete All Chats\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Are you sure you want to delete all your chat history? This action cannot be undone and will permanently remove all conversations.\n            </p>\n            <div className=\"flex justify-end gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowDeleteAllConfirm(false)}\n              >\n                Cancel\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={deleteAllChatSessions}\n                className=\"bg-red-600 hover:bg-red-700 text-white\"\n              >\n                Delete All\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAwBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD,EAAE;QACX;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB,KAAK,YAAY,IAAI,EAAE;YACzC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,WAAW;YAC/D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,WAAW,CAAC,QAAQ,IAAI,EAAE;gBAC3C,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,eAAe;QACnB,YAAY,EAAE;QACd,oBAAoB;IACtB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE;gBACrD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,0BAA0B;gBAC1B,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;gBAE9D,4CAA4C;gBAC5C,IAAI,qBAAqB,WAAW;oBAClC,YAAY,EAAE;oBACd,oBAAoB;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;QACA,qBAAqB;IACvB;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB,EAAE;gBAClB,YAAY,EAAE;gBACd,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;QACA,wBAAwB;IAC1B;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAAc,aAAa,IAAI;QACrC,gBAAgB;QAChB,aAAa;QAEb,qCAAqC;QACrC,MAAM,kBAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;QAE9C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,eAAe;gBACjB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,2CAA2C;gBAC3C,IAAI,CAAC,kBAAkB;oBACrB,oBAAoB,KAAK,aAAa;oBACtC,mBAAmB,4BAA4B;;gBACjD;gBAEA,yBAAyB;gBACzB,MAAM,mBAA4B;oBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,SAAS,KAAK,OAAO,IAAI;oBACzB,MAAM;oBACN,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;YACjD,OAAO;gBACL,eAAe;gBACf,MAAM,eAAwB;oBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,SAAS;oBACT,MAAM;oBACN,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAa;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAW,GAAG,gBAAgB,SAAS,MAAM,4FAA4F,CAAC;;0CAC7I,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,wBAAwB;wDACvC,WAAU;wDACV,UAAU,aAAa,MAAM,KAAK;kEAElC,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,iBAAiB;kEAEhC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAInB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;oCACtD,aAAa,MAAM,KAAK,kBACvB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;6DAErC,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,8OAAC;gDAEC,WAAW,CAAC,4CAA4C,EACtD,qBAAqB,QAAQ,EAAE,GAAG,0CAA0C,oBAC5E;;kEAEF,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,gBAAgB,QAAQ,EAAE;wDACzC,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,wNAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;kFACzB,8OAAC;wEAAK,WAAU;kFACb,QAAQ,KAAK,IAAI;;;;;;;;;;;;0EAGtB,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;kEAKnD,8OAAC;wDACC,MAAK;wDACL,OAAM;wDACN,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,qBAAqB,QAAQ,EAAE;wDACjC;wDACA,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;+CA/Bf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAyC3B,8OAAC;wBAAI,WAAU;;4BAEZ,CAAC,+BACA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAOlC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;yDAMjC,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;0DAE9E,cAAA,8OAAC;oDAAI,WAAW,CAAC,2BAA2B,EAAE,QAAQ,IAAI,KAAK,SAAS,qCAAqC,IAAI;;sEAE/G,8OAAC;4DAAI,WAAW,CAAC,wFAAwF,EACvG,QAAQ,IAAI,KAAK,SACb,6BACA,6BACJ;sEACC,QAAQ,IAAI,KAAK,SAAS,MAAM;;;;;;sEAInC,8OAAC;4DACC,WAAW,CAAC,qDAAqD,EAC/D,QAAQ,IAAI,KAAK,SACb,2CACA,+DACJ;;8EAEF,8OAAC;oEAAE,WAAU;8EACV,QAAQ,OAAO;;;;;;8EAElB,8OAAC;oEAAI,WAAW,CAAC,aAAa,EAC5B,QAAQ,IAAI,KAAK,SAAS,oBAAoB,iBAC9C;8EACC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,EAAE,EAAE;wEAClD,MAAM;wEACN,QAAQ;oEACV;;;;;;;;;;;;;;;;;;+CA9BD,QAAQ,EAAE;;;;;wCAoClB,2BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEAAoH;;;;;;kEAKnI,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMzB,8OAAC;4CAAI,KAAK;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY;gDACZ,aAAY;gDACZ,WAAU;gDACV,MAAM;gDACN,UAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU,CAAC,aAAa,IAAI,MAAM;gDAClC,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS3B,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,qBAAqB;8CACrC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,sCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,wBAAwB;8CACxC;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}