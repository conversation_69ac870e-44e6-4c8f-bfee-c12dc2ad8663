{"version": 3, "sources": ["../../../src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "actionAsyncStorage", "window", "require", "undefined", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "getStore", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;AAEzB,MAAMC,qBACJ,OAAOC,WAAW,cACd,AACEC,QAAQ,yDACRF,kBAAkB,GACpBG;AAEN,OAAO,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,uBAAAA,aAAiCX,mBAAmBY,iBAAiB;IAErE,MAAMC,QAAQ,qBAA8B,CAA9B,IAAIC,MAAMX,sBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CU,MAAME,MAAM,GAAG,AAAGZ,sBAAoB,MAAGO,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOE;AACT;AAEA;;;;;;;;;;;CAWC,GACD,OAAO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;QAEVN;IAATM,eAAAA,OAAAA,OAASN,CAAAA,uCAAAA,+BAAAA,mBAAoBa,QAAQ,uBAA5Bb,6BAAgCc,QAAQ,IAC7CjB,aAAakB,IAAI,GACjBlB,aAAamB,OAAO;IAExB,MAAMZ,iBAAiBC,KAAKC,MAAMV,mBAAmBY,iBAAiB;AACxE;AAEA;;;;;;;;;;CAUC,GACD,OAAO,SAASS,kBACd,2BAA2B,GAC3BZ,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OAAqBT,aAAamB,OAAO;IAEzC,MAAMZ,iBAAiBC,KAAKC,MAAMV,mBAAmBsB,iBAAiB;AACxE;AAUA,OAAO,SAASC,wBAAwBV,KAAc;IACpD,IAAI,CAACX,gBAAgBW,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEA,OAAO,SAASC,yBAAyBd,KAAoB;IAC3D,IAAI,CAACX,gBAAgBW,QAAQ;QAC3B,MAAM,qBAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEA,OAAO,SAASI,+BAA+Bf,KAAoB;IACjE,IAAI,CAACX,gBAAgBW,QAAQ;QAC3B,MAAM,qBAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOe,OAAOhB,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C"}