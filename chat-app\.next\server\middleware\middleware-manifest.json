{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/chat/:path*{(\\\\.json)}?", "originalSource": "/chat/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b+bbPJUZuzuCkZDLTeFmftb+bkXkGidpzeWNLZfQMDI=", "__NEXT_PREVIEW_MODE_ID": "fe3b8c97edbedfd90b44e3c4a2ff9c82", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "726689fb00d48169ca19778339c0e65612506bc8ccf99157ca33401fcce4d7e4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3cfdb222a5b6a6f1cef328d2da56a7cecf1e168f7c38899ea9bca8c9cbc756c5"}}}, "instrumentation": null, "functions": {}}