{"version": 3, "sources": ["../../src/client/remove-locale.ts"], "sourcesContent": ["import { parsePath } from '../shared/lib/router/utils/parse-path'\n\nexport function removeLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { pathname } = parsePath(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale?.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith(`/${localeLower}/`) ||\n        pathLower === `/${localeLower}`)\n      ? `${pathname.length === locale.length + 1 ? `/` : ``}${path.slice(\n          locale.length + 1\n        )}`\n      : path\n  }\n  return path\n}\n"], "names": ["removeLocale", "path", "locale", "process", "env", "__NEXT_I18N_SUPPORT", "pathname", "parsePath", "pathLower", "toLowerCase", "localeLower", "startsWith", "length", "slice"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;2BAFU;AAEnB,SAASA,aAAaC,IAAY,EAAEC,MAAe;IACxD,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;QACnC,MAAM,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,oBAAS,EAACN;QAC/B,MAAMO,YAAYF,SAASG,WAAW;QACtC,MAAMC,cAAcR,0BAAAA,OAAQO,WAAW;QAEvC,OAAOP,UACJM,CAAAA,UAAUG,UAAU,CAAC,AAAC,MAAGD,cAAY,QACpCF,cAAc,AAAC,MAAGE,WAAY,IAC9B,AAAC,KAAEJ,CAAAA,SAASM,MAAM,KAAKV,OAAOU,MAAM,GAAG,IAAK,MAAM,EAAA,IAAIX,KAAKY,KAAK,CAC9DX,OAAOU,MAAM,GAAG,KAElBX;IACN;IACA,OAAOA;AACT"}