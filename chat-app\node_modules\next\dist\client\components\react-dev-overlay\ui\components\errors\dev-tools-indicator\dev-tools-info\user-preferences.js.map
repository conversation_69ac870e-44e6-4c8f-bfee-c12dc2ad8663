{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.tsx"], "sourcesContent": ["import { useState, type HTMLProps } from 'react'\nimport { css } from '../../../../../utils/css'\nimport EyeIcon from '../../../../icons/eye-icon'\nimport { STORAGE_KEY_POSITION, STORAGE_KEY_THEME } from '../../../../../shared'\nimport LightIcon from '../../../../icons/light-icon'\nimport DarkIcon from '../../../../icons/dark-icon'\nimport SystemIcon from '../../../../icons/system-icon'\nimport type { DevToolsInfoPropsCore } from './dev-tools-info'\nimport { DevToolsInfo } from './dev-tools-info'\nimport {\n  getInitialTheme,\n  NEXT_DEV_TOOLS_SCALE,\n  type DevToolsIndicatorPosition,\n  type DevToolsScale,\n} from './preferences'\n\nexport function UserPreferences({\n  setPosition,\n  position,\n  hide,\n  scale,\n  setScale,\n  ...props\n}: {\n  setPosition: (position: DevToolsIndicatorPosition) => void\n  position: DevToolsIndicatorPosition\n  scale: DevToolsScale\n  setScale: (value: DevToolsScale) => void\n  hide: () => void\n} & DevToolsInfoPropsCore &\n  Omit<HTMLProps<HTMLDivElement>, 'size'>) {\n  // derive initial theme from system preference\n  const [theme, setTheme] = useState(getInitialTheme())\n\n  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const portal = document.querySelector('nextjs-portal')\n    if (!portal) return\n\n    setTheme(e.target.value)\n\n    if (e.target.value === 'system') {\n      portal.classList.remove('dark')\n      portal.classList.remove('light')\n      localStorage.removeItem(STORAGE_KEY_THEME)\n      return\n    }\n\n    if (e.target.value === 'dark') {\n      portal.classList.add('dark')\n      portal.classList.remove('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'dark')\n    } else {\n      portal.classList.remove('dark')\n      portal.classList.add('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'light')\n    }\n  }\n\n  function handlePositionChange(e: React.ChangeEvent<HTMLSelectElement>) {\n    setPosition(e.target.value as DevToolsIndicatorPosition)\n    localStorage.setItem(STORAGE_KEY_POSITION, e.target.value)\n  }\n\n  function handleSizeChange({ target }: React.ChangeEvent<HTMLSelectElement>) {\n    const value = Number(target.value) as DevToolsScale\n    setScale(value)\n  }\n\n  return (\n    <DevToolsInfo title=\"Preferences\" {...props}>\n      <div className=\"preferences-container\">\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"theme\">Theme</label>\n            <p className=\"preference-description\">\n              Select your theme preference.\n            </p>\n          </div>\n          <Select\n            id=\"theme\"\n            name=\"theme\"\n            prefix={<ThemeIcon theme={theme as 'dark' | 'light' | 'system'} />}\n            value={theme}\n            onChange={handleThemeChange}\n          >\n            <option value=\"system\">System</option>\n            <option value=\"light\">Light</option>\n            <option value=\"dark\">Dark</option>\n          </Select>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"position\">Position</label>\n            <p className=\"preference-description\">\n              Adjust the placement of your dev tools.\n            </p>\n          </div>\n          <Select\n            id=\"position\"\n            name=\"position\"\n            value={position}\n            onChange={handlePositionChange}\n          >\n            <option value=\"bottom-left\">Bottom Left</option>\n            <option value=\"bottom-right\">Bottom Right</option>\n            <option value=\"top-left\">Top Left</option>\n            <option value=\"top-right\">Top Right</option>\n          </Select>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"size\">Size</label>\n            <p className=\"preference-description\">\n              Adjust the size of your dev tools.\n            </p>\n          </div>\n          <Select\n            id=\"size\"\n            name=\"size\"\n            value={scale}\n            onChange={handleSizeChange}\n          >\n            {Object.entries(NEXT_DEV_TOOLS_SCALE).map(([key, value]) => {\n              return (\n                <option value={value} key={key}>\n                  {key}\n                </option>\n              )\n            })}\n          </Select>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label id=\"hide-dev-tools\">Hide Dev Tools for this session</label>\n            <p className=\"preference-description\">\n              Hide Dev Tools until you restart your dev server, or 1 day.\n            </p>\n          </div>\n          <div className=\"preference-control\">\n            <button\n              aria-describedby=\"hide-dev-tools\"\n              name=\"hide-dev-tools\"\n              data-hide-dev-tools\n              className=\"action-button\"\n              onClick={hide}\n            >\n              <EyeIcon />\n              <span>Hide</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label>Disable Dev Tools for this project</label>\n            <p className=\"preference-description\">\n              To disable this UI completely, set{' '}\n              <code className=\"dev-tools-info-code\">devIndicators: false</code>{' '}\n              in your <code className=\"dev-tools-info-code\">next.config</code>{' '}\n              file.\n            </p>\n          </div>\n        </div>\n      </div>\n    </DevToolsInfo>\n  )\n}\n\nfunction Select({\n  children,\n  prefix,\n  ...props\n}: {\n  prefix?: React.ReactNode\n} & Omit<React.HTMLProps<HTMLSelectElement>, 'prefix'>) {\n  return (\n    <div className=\"select-button\">\n      {prefix}\n      <select {...props}>{children}</select>\n      <ChevronDownIcon />\n    </div>\n  )\n}\n\nfunction ThemeIcon({ theme }: { theme: 'dark' | 'light' | 'system' }) {\n  switch (theme) {\n    case 'system':\n      return <SystemIcon />\n    case 'dark':\n      return <DarkIcon />\n    case 'light':\n      return <LightIcon />\n    default:\n      return null\n  }\n}\n\nexport const DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = css`\n  .preferences-container {\n    padding: 8px 6px;\n    width: 100%;\n  }\n\n  @media (min-width: 576px) {\n    .preferences-container {\n      width: 480px;\n    }\n  }\n\n  .preference-section:first-child {\n    padding-top: 0;\n  }\n\n  .preference-section {\n    padding: 12px 0;\n    border-bottom: 1px solid var(--color-gray-400);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 24px;\n  }\n\n  .preference-section:last-child {\n    border-bottom: none;\n  }\n\n  .preference-header {\n    margin-bottom: 0;\n    flex: 1;\n  }\n\n  .preference-header label {\n    font-size: var(--size-14);\n    font-weight: 500;\n    color: var(--color-gray-1000);\n    margin: 0;\n  }\n\n  .preference-description {\n    color: var(--color-gray-900);\n    font-size: var(--size-14);\n    margin: 0;\n  }\n\n  .select-button,\n  .action-button {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-400);\n    border-radius: var(--rounded-lg);\n    font-weight: 400;\n    font-size: var(--size-14);\n    color: var(--color-gray-1000);\n    padding: 6px 8px;\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  .select-button {\n    &:focus-within {\n      outline: var(--focus-ring);\n    }\n\n    select {\n      all: unset;\n    }\n  }\n\n  :global(.icon) {\n    width: 18px;\n    height: 18px;\n    color: #666;\n  }\n`\n\nfunction ChevronDownIcon() {\n  return (\n    <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" aria-hidden>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M14.0607 5.49999L13.5303 6.03032L8.7071 10.8535C8.31658 11.2441 7.68341 11.2441 7.29289 10.8535L2.46966 6.03032L1.93933 5.49999L2.99999 4.43933L3.53032 4.96966L7.99999 9.43933L12.4697 4.96966L13 4.43933L14.0607 5.49999Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["DEV_TOOLS_INFO_USER_PREFERENCES_STYLES", "UserPreferences", "setPosition", "position", "hide", "scale", "setScale", "props", "theme", "setTheme", "useState", "getInitialTheme", "handleThemeChange", "e", "portal", "document", "querySelector", "target", "value", "classList", "remove", "localStorage", "removeItem", "STORAGE_KEY_THEME", "add", "setItem", "handlePositionChange", "STORAGE_KEY_POSITION", "handleSizeChange", "Number", "DevToolsInfo", "title", "div", "className", "label", "htmlFor", "p", "Select", "id", "name", "prefix", "ThemeIcon", "onChange", "option", "Object", "entries", "NEXT_DEV_TOOLS_SCALE", "map", "key", "button", "aria-<PERSON><PERSON>", "data-hide-dev-tools", "onClick", "EyeIcon", "span", "code", "children", "select", "ChevronDownIcon", "SystemIcon", "DarkIcon", "LightIcon", "css", "svg", "width", "height", "viewBox", "aria-hidden", "path", "fillRule", "clipRule", "d", "fill"], "mappings": ";;;;;;;;;;;;;;;IAwMaA,sCAAsC;eAAtCA;;IAxLGC,eAAe;eAAfA;;;;;;uBAhByB;qBACrB;kEACA;wBACoC;oEAClC;mEACD;qEACE;8BAEM;6BAMtB;;;;;;;;;;AAEA,SAASA,gBAAgB,KAcS;IAdT,IAAA,EAC9BC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACR,GAAGC,OAQoC,GAdT;IAe9B,8CAA8C;IAC9C,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,eAAQ,EAACC,IAAAA,4BAAe;IAElD,MAAMC,oBAAoB,CAACC;QACzB,MAAMC,SAASC,SAASC,aAAa,CAAC;QACtC,IAAI,CAACF,QAAQ;QAEbL,SAASI,EAAEI,MAAM,CAACC,KAAK;QAEvB,IAAIL,EAAEI,MAAM,CAACC,KAAK,KAAK,UAAU;YAC/BJ,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBC,aAAaC,UAAU,CAACC,yBAAiB;YACzC;QACF;QAEA,IAAIV,EAAEI,MAAM,CAACC,KAAK,KAAK,QAAQ;YAC7BJ,OAAOK,SAAS,CAACK,GAAG,CAAC;YACrBV,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBC,aAAaI,OAAO,CAACF,yBAAiB,EAAE;QAC1C,OAAO;YACLT,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACK,GAAG,CAAC;YACrBH,aAAaI,OAAO,CAACF,yBAAiB,EAAE;QAC1C;IACF;IAEA,SAASG,qBAAqBb,CAAuC;QACnEX,YAAYW,EAAEI,MAAM,CAACC,KAAK;QAC1BG,aAAaI,OAAO,CAACE,4BAAoB,EAAEd,EAAEI,MAAM,CAACC,KAAK;IAC3D;IAEA,SAASU,iBAAiB,KAAgD;QAAhD,IAAA,EAAEX,MAAM,EAAwC,GAAhD;QACxB,MAAMC,QAAQW,OAAOZ,OAAOC,KAAK;QACjCZ,SAASY;IACX;IAEA,qBACE,qBAACY,0BAAY;QAACC,OAAM;QAAe,GAAGxB,KAAK;kBACzC,cAAA,sBAACyB;YAAIC,WAAU;;8BACb,sBAACD;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMC,SAAQ;8CAAQ;;8CACvB,qBAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,sBAACI;4BACCC,IAAG;4BACHC,MAAK;4BACLC,sBAAQ,qBAACC;gCAAUjC,OAAOA;;4BAC1BU,OAAOV;4BACPkC,UAAU9B;;8CAEV,qBAAC+B;oCAAOzB,OAAM;8CAAS;;8CACvB,qBAACyB;oCAAOzB,OAAM;8CAAQ;;8CACtB,qBAACyB;oCAAOzB,OAAM;8CAAO;;;;;;8BAIzB,sBAACc;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMC,SAAQ;8CAAW;;8CAC1B,qBAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,sBAACI;4BACCC,IAAG;4BACHC,MAAK;4BACLrB,OAAOf;4BACPuC,UAAUhB;;8CAEV,qBAACiB;oCAAOzB,OAAM;8CAAc;;8CAC5B,qBAACyB;oCAAOzB,OAAM;8CAAe;;8CAC7B,qBAACyB;oCAAOzB,OAAM;8CAAW;;8CACzB,qBAACyB;oCAAOzB,OAAM;8CAAY;;;;;;8BAI9B,sBAACc;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMC,SAAQ;8CAAO;;8CACtB,qBAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,qBAACI;4BACCC,IAAG;4BACHC,MAAK;4BACLrB,OAAOb;4BACPqC,UAAUd;sCAETgB,OAAOC,OAAO,CAACC,iCAAoB,EAAEC,GAAG,CAAC;oCAAC,CAACC,KAAK9B,MAAM;gCACrD,qBACE,qBAACyB;oCAAOzB,OAAOA;8CACZ8B;mCADwBA;4BAI/B;;;;8BAIJ,sBAAChB;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACC;oCAAMI,IAAG;8CAAiB;;8CAC3B,qBAACF;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,qBAACD;4BAAIC,WAAU;sCACb,cAAA,sBAACgB;gCACCC,oBAAiB;gCACjBX,MAAK;gCACLY,qBAAmB;gCACnBlB,WAAU;gCACVmB,SAAShD;;kDAET,qBAACiD,gBAAO;kDACR,qBAACC;kDAAK;;;;;;;8BAKZ,qBAACtB;oBAAIC,WAAU;8BACb,cAAA,sBAACD;wBAAIC,WAAU;;0CACb,qBAACC;0CAAM;;0CACP,sBAACE;gCAAEH,WAAU;;oCAAyB;oCACD;kDACnC,qBAACsB;wCAAKtB,WAAU;kDAAsB;;oCAA4B;oCAAI;kDAC9D,qBAACsB;wCAAKtB,WAAU;kDAAsB;;oCAAmB;oCAAI;;;;;;;;;AAQnF;AAEA,SAASI,OAAO,KAMsC;IANtC,IAAA,EACdmB,QAAQ,EACRhB,MAAM,EACN,GAAGjC,OAGiD,GANtC;IAOd,qBACE,sBAACyB;QAAIC,WAAU;;YACZO;0BACD,qBAACiB;gBAAQ,GAAGlD,KAAK;0BAAGiD;;0BACpB,qBAACE;;;AAGP;AAEA,SAASjB,UAAU,KAAiD;IAAjD,IAAA,EAAEjC,KAAK,EAA0C,GAAjD;IACjB,OAAQA;QACN,KAAK;YACH,qBAAO,qBAACmD,mBAAU;QACpB,KAAK;YACH,qBAAO,qBAACC,iBAAQ;QAClB,KAAK;YACH,qBAAO,qBAACC,kBAAS;QACnB;YACE,OAAO;IACX;AACF;AAEO,MAAM7D,6CAAyC8D,QAAG;AAkFzD,SAASJ;IACP,qBACE,qBAACK;QAAIC,OAAM;QAAKC,QAAO;QAAKC,SAAQ;QAAYC,aAAW;kBACzD,cAAA,qBAACC;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFC,MAAK;;;AAIb"}