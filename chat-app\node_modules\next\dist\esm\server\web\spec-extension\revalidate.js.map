{"version": 3, "sources": ["../../../../src/server/web/spec-extension/revalidate.ts"], "sourcesContent": ["import {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n} from '../../app-render/dynamic-rendering'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils'\nimport {\n  NEXT_CACHE_IMPLICIT_TAG_ID,\n  NEXT_CACHE_SOFT_TAG_MAX_LENGTH,\n} from '../../../lib/constants'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific cache tag.\n *\n * Read more: [Next.js Docs: `revalidateTag`](https://nextjs.org/docs/app/api-reference/functions/revalidateTag)\n */\nexport function revalidateTag(tag: string) {\n  return revalidate([tag], `revalidateTag ${tag}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific path.\n *\n * Read more: [Next.js Docs: `unstable_expirePath`](https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath)\n */\nexport function unstable_expirePath(\n  originalPath: string,\n  type?: 'layout' | 'page'\n) {\n  if (originalPath.length > NEXT_CACHE_SOFT_TAG_MAX_LENGTH) {\n    console.warn(\n      `Warning: expirePath received \"${originalPath}\" which exceeded max length of ${NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`\n    )\n    return\n  }\n\n  let normalizedPath = `${NEXT_CACHE_IMPLICIT_TAG_ID}${originalPath}`\n\n  if (type) {\n    normalizedPath += `${normalizedPath.endsWith('/') ? '' : '/'}${type}`\n  } else if (isDynamicRoute(originalPath)) {\n    console.warn(\n      `Warning: a dynamic page path \"${originalPath}\" was passed to \"expirePath\", but the \"type\" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`\n    )\n  }\n  return revalidate([normalizedPath], `unstable_expirePath ${originalPath}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific cache tag.\n *\n * Read more: [Next.js Docs: `unstable_expireTag`](https://nextjs.org/docs/app/api-reference/functions/unstable_expireTag)\n */\nexport function unstable_expireTag(...tags: string[]) {\n  return revalidate(tags, `unstable_expireTag ${tags.join(', ')}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific path.\n *\n * Read more: [Next.js Docs: `revalidatePath`](https://nextjs.org/docs/app/api-reference/functions/revalidatePath)\n */\nexport function revalidatePath(originalPath: string, type?: 'layout' | 'page') {\n  if (originalPath.length > NEXT_CACHE_SOFT_TAG_MAX_LENGTH) {\n    console.warn(\n      `Warning: revalidatePath received \"${originalPath}\" which exceeded max length of ${NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`\n    )\n    return\n  }\n\n  let normalizedPath = `${NEXT_CACHE_IMPLICIT_TAG_ID}${originalPath}`\n\n  if (type) {\n    normalizedPath += `${normalizedPath.endsWith('/') ? '' : '/'}${type}`\n  } else if (isDynamicRoute(originalPath)) {\n    console.warn(\n      `Warning: a dynamic page path \"${originalPath}\" was passed to \"revalidatePath\", but the \"type\" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`\n    )\n  }\n  return revalidate([normalizedPath], `revalidatePath ${originalPath}`)\n}\n\nfunction revalidate(tags: string[], expression: string) {\n  const store = workAsyncStorage.getStore()\n  if (!store || !store.incrementalCache) {\n    throw new Error(\n      `Invariant: static generation store missing in ${expression}`\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a \"use cache\" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    } else if (workUnitStore.type === 'unstable-cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n    if (workUnitStore.phase === 'render') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore.type === 'prerender') {\n      // dynamicIO Prerender\n      const error = new Error(\n        `Route ${store.route} used ${expression} without first calling \\`await connection()\\`.`\n      )\n      abortAndThrowOnSynchronousRequestDataAccess(\n        store.route,\n        expression,\n        error,\n        workUnitStore\n      )\n    } else if (workUnitStore.type === 'prerender-ppr') {\n      // PPR Prerender\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      // legacy Prerender\n      workUnitStore.revalidate = 0\n\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n\n  if (!store.pendingRevalidatedTags) {\n    store.pendingRevalidatedTags = []\n  }\n\n  for (const tag of tags) {\n    if (!store.pendingRevalidatedTags.includes(tag)) {\n      store.pendingRevalidatedTags.push(tag)\n    }\n  }\n\n  // TODO: only revalidate if the path matches\n  store.pathWasRevalidated = true\n}\n"], "names": ["abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "isDynamicRoute", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "workAsyncStorage", "workUnitAsyncStorage", "DynamicServerError", "revalidateTag", "tag", "revalidate", "unstable_expirePath", "originalPath", "type", "length", "console", "warn", "normalizedPath", "endsWith", "unstable_expireTag", "tags", "join", "revalidatePath", "expression", "store", "getStore", "incrementalCache", "Error", "workUnitStore", "route", "phase", "error", "dynamicTracking", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "pendingRevalidatedTags", "includes", "push", "pathWasRevalidated"], "mappings": "AAAA,SACEA,2CAA2C,EAC3CC,oBAAoB,QACf,qCAAoC;AAC3C,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,0BAA0B,EAC1BC,8BAA8B,QACzB,yBAAwB;AAC/B,SAASC,gBAAgB,QAAQ,+CAA8C;AAC/E,SAASC,oBAAoB,QAAQ,oDAAmD;AACxF,SAASC,kBAAkB,QAAQ,kDAAiD;AAEpF;;;;CAIC,GACD,OAAO,SAASC,cAAcC,GAAW;IACvC,OAAOC,WAAW;QAACD;KAAI,EAAE,CAAC,cAAc,EAAEA,KAAK;AACjD;AAEA;;;;CAIC,GACD,OAAO,SAASE,oBACdC,YAAoB,EACpBC,IAAwB;IAExB,IAAID,aAAaE,MAAM,GAAGV,gCAAgC;QACxDW,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEJ,aAAa,+BAA+B,EAAER,+BAA+B,4FAA4F,CAAC;QAE7M;IACF;IAEA,IAAIa,iBAAiB,GAAGd,6BAA6BS,cAAc;IAEnE,IAAIC,MAAM;QACRI,kBAAkB,GAAGA,eAAeC,QAAQ,CAAC,OAAO,KAAK,MAAML,MAAM;IACvE,OAAO,IAAIX,eAAeU,eAAe;QACvCG,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEJ,aAAa,4LAA4L,CAAC;IAE/O;IACA,OAAOF,WAAW;QAACO;KAAe,EAAE,CAAC,oBAAoB,EAAEL,cAAc;AAC3E;AAEA;;;;CAIC,GACD,OAAO,SAASO,mBAAmB,GAAGC,IAAc;IAClD,OAAOV,WAAWU,MAAM,CAAC,mBAAmB,EAAEA,KAAKC,IAAI,CAAC,OAAO;AACjE;AAEA;;;;CAIC,GACD,OAAO,SAASC,eAAeV,YAAoB,EAAEC,IAAwB;IAC3E,IAAID,aAAaE,MAAM,GAAGV,gCAAgC;QACxDW,QAAQC,IAAI,CACV,CAAC,kCAAkC,EAAEJ,aAAa,+BAA+B,EAAER,+BAA+B,uFAAuF,CAAC;QAE5M;IACF;IAEA,IAAIa,iBAAiB,GAAGd,6BAA6BS,cAAc;IAEnE,IAAIC,MAAM;QACRI,kBAAkB,GAAGA,eAAeC,QAAQ,CAAC,OAAO,KAAK,MAAML,MAAM;IACvE,OAAO,IAAIX,eAAeU,eAAe;QACvCG,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEJ,aAAa,2LAA2L,CAAC;IAE9O;IACA,OAAOF,WAAW;QAACO;KAAe,EAAE,CAAC,eAAe,EAAEL,cAAc;AACtE;AAEA,SAASF,WAAWU,IAAc,EAAEG,UAAkB;IACpD,MAAMC,QAAQnB,iBAAiBoB,QAAQ;IACvC,IAAI,CAACD,SAAS,CAACA,MAAME,gBAAgB,EAAE;QACrC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,8CAA8C,EAAEJ,YAAY,GADzD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,gBAAgBtB,qBAAqBmB,QAAQ;IACnD,IAAIG,eAAe;QACjB,IAAIA,cAAcf,IAAI,KAAK,SAAS;YAClC,MAAM,qBAEL,CAFK,IAAIc,MACR,CAAC,MAAM,EAAEH,MAAMK,KAAK,CAAC,OAAO,EAAEN,WAAW,qRAAqR,CAAC,GAD3T,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIK,cAAcf,IAAI,KAAK,kBAAkB;YAClD,MAAM,qBAEL,CAFK,IAAIc,MACR,CAAC,MAAM,EAAEH,MAAMK,KAAK,CAAC,OAAO,EAAEN,WAAW,oTAAoT,CAAC,GAD1V,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIK,cAAcE,KAAK,KAAK,UAAU;YACpC,MAAM,qBAEL,CAFK,IAAIH,MACR,CAAC,MAAM,EAAEH,MAAMK,KAAK,CAAC,OAAO,EAAEN,WAAW,8QAA8Q,CAAC,GADpT,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIK,cAAcf,IAAI,KAAK,aAAa;YACtC,sBAAsB;YACtB,MAAMkB,QAAQ,qBAEb,CAFa,IAAIJ,MAChB,CAAC,MAAM,EAAEH,MAAMK,KAAK,CAAC,MAAM,EAAEN,WAAW,8CAA8C,CAAC,GAD3E,qBAAA;uBAAA;4BAAA;8BAAA;YAEd;YACAvB,4CACEwB,MAAMK,KAAK,EACXN,YACAQ,OACAH;QAEJ,OAAO,IAAIA,cAAcf,IAAI,KAAK,iBAAiB;YACjD,gBAAgB;YAChBZ,qBACEuB,MAAMK,KAAK,EACXN,YACAK,cAAcI,eAAe;QAEjC,OAAO,IAAIJ,cAAcf,IAAI,KAAK,oBAAoB;YACpD,mBAAmB;YACnBe,cAAclB,UAAU,GAAG;YAE3B,MAAMuB,MAAM,qBAEX,CAFW,IAAI1B,mBACd,CAAC,MAAM,EAAEiB,MAAMK,KAAK,CAAC,mDAAmD,EAAEN,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMU,uBAAuB,GAAGX;YAChCC,MAAMW,iBAAiB,GAAGF,IAAIG,KAAK;YAEnC,MAAMH;QACR,OAAO,IACLI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBX,iBACAA,cAAcf,IAAI,KAAK,WACvB;YACAe,cAAcY,WAAW,GAAG;QAC9B;IACF;IAEA,IAAI,CAAChB,MAAMiB,sBAAsB,EAAE;QACjCjB,MAAMiB,sBAAsB,GAAG,EAAE;IACnC;IAEA,KAAK,MAAMhC,OAAOW,KAAM;QACtB,IAAI,CAACI,MAAMiB,sBAAsB,CAACC,QAAQ,CAACjC,MAAM;YAC/Ce,MAAMiB,sBAAsB,CAACE,IAAI,CAAClC;QACpC;IACF;IAEA,4CAA4C;IAC5Ce,MAAMoB,kBAAkB,GAAG;AAC7B"}