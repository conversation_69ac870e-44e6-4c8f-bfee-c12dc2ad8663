{"version": 3, "sources": ["../../../src/experimental/testmode/httpget.ts"], "sourcesContent": ["import { ClientRequestInterceptor } from 'next/dist/compiled/@mswjs/interceptors/ClientRequest'\nimport { handleFetch } from './fetch'\n\ntype Fetch = typeof fetch\n\nexport function interceptHttpGet(originalFetch: Fetch): () => void {\n  const clientRequestInterceptor = new ClientRequestInterceptor()\n  clientRequestInterceptor.on('request', async ({ request }) => {\n    const response = await handleFetch(originalFetch, request)\n    request.respondWith(response)\n  })\n  clientRequestInterceptor.apply()\n\n  // Cleanup.\n  return () => {\n    clientRequestInterceptor.dispose()\n  }\n}\n"], "names": ["interceptHttpGet", "originalFetch", "clientRequestInterceptor", "ClientRequestInterceptor", "on", "request", "response", "handleFetch", "respondWith", "apply", "dispose"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;+BALyB;uBACb;AAIrB,SAASA,iBAAiBC,aAAoB;IACnD,MAAMC,2BAA2B,IAAIC,uCAAwB;IAC7DD,yBAAyBE,EAAE,CAAC,WAAW,OAAO,EAAEC,OAAO,EAAE;QACvD,MAAMC,WAAW,MAAMC,IAAAA,kBAAW,EAACN,eAAeI;QAClDA,QAAQG,WAAW,CAACF;IACtB;IACAJ,yBAAyBO,KAAK;IAE9B,WAAW;IACX,OAAO;QACLP,yBAAyBQ,OAAO;IAClC;AACF"}