{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/container/runtime-error/render-error.tsx"], "sourcesContent": ["import type {\n  OverlayState,\n  UnhandledErrorAction,\n  UnhandledRejectionAction,\n} from '../../../shared'\n\nimport { useMemo, useState, useEffect } from 'react'\nimport {\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n} from '../../../shared'\nimport {\n  getErrorByType,\n  type ReadyRuntimeError,\n} from '../../../utils/get-error-by-type'\n\nexport type SupportedErrorEvent = {\n  id: number\n  event: UnhandledErrorAction | UnhandledRejectionAction\n}\n\nfunction getErrorSignature(ev: SupportedErrorEvent): string {\n  const { event } = ev\n  // eslint-disable-next-line default-case -- <PERSON><PERSON> checks this\n  switch (event.type) {\n    case ACTION_UNHANDLED_ERROR:\n    case ACTION_UNHANDLED_REJECTION: {\n      return `${event.reason.name}::${event.reason.message}::${event.reason.stack}`\n    }\n  }\n}\n\ntype Props = {\n  children: (params: {\n    runtimeErrors: ReadyRuntimeError[]\n    totalErrorCount: number\n  }) => React.ReactNode\n  state: OverlayState\n  isAppDir: boolean\n}\n\nexport const RenderError = (props: Props) => {\n  const { state } = props\n  const isBuildError = !!state.buildError\n\n  if (isBuildError) {\n    return <RenderBuildError {...props} />\n  } else {\n    return <RenderRuntimeError {...props} />\n  }\n}\n\nconst RenderRuntimeError = ({ children, state, isAppDir }: Props) => {\n  const { errors } = state\n\n  const [lookups, setLookups] = useState<{\n    [eventId: string]: ReadyRuntimeError\n  }>({})\n\n  const [runtimeErrors, nextError] = useMemo<\n    [ReadyRuntimeError[], SupportedErrorEvent | null]\n  >(() => {\n    let ready: ReadyRuntimeError[] = []\n    let next: SupportedErrorEvent | null = null\n\n    // Ensure errors are displayed in the order they occurred in:\n    for (let idx = 0; idx < errors.length; ++idx) {\n      const e = errors[idx]\n      const { id } = e\n      if (id in lookups) {\n        ready.push(lookups[id])\n        continue\n      }\n\n      // Check for duplicate errors\n      if (idx > 0) {\n        const prev = errors[idx - 1]\n        if (getErrorSignature(prev) === getErrorSignature(e)) {\n          continue\n        }\n      }\n\n      next = e\n      break\n    }\n\n    return [ready, next]\n  }, [errors, lookups])\n\n  useEffect(() => {\n    if (nextError == null) {\n      return\n    }\n\n    let mounted = true\n\n    getErrorByType(nextError, isAppDir).then((resolved) => {\n      if (mounted) {\n        // We don't care if the desired error changed while we were resolving,\n        // thus we're not tracking it using a ref. Once the work has been done,\n        // we'll store it.\n        setLookups((m) => ({ ...m, [resolved.id]: resolved }))\n      }\n    })\n\n    return () => {\n      mounted = false\n    }\n  }, [nextError, isAppDir])\n\n  const totalErrorCount = errors.filter((err, idx) => {\n    const prev = errors[idx - 1]\n    // Check for duplicates\n    if (idx > 0) return getErrorSignature(prev) !== getErrorSignature(err)\n    return true\n  }).length\n\n  return children({ runtimeErrors, totalErrorCount })\n}\n\nconst RenderBuildError = ({ children }: Props) => {\n  return children({\n    runtimeErrors: [],\n    // Build errors and missing root layout tags persist until fixed,\n    // so we can set a fixed error count of 1\n    totalErrorCount: 1,\n  })\n}\n"], "names": ["useMemo", "useState", "useEffect", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "getErrorByType", "getErrorSignature", "ev", "event", "type", "reason", "name", "message", "stack", "RenderError", "props", "state", "isBuildError", "buildError", "RenderBuildError", "RenderRuntimeError", "children", "isAppDir", "errors", "lookups", "setLookups", "runtimeErrors", "nextError", "ready", "next", "idx", "length", "e", "id", "push", "prev", "mounted", "then", "resolved", "m", "totalErrorCount", "filter", "err"], "mappings": ";AAMA,SAASA,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,QAAO;AACpD,SACEC,sBAAsB,EACtBC,0BAA0B,QACrB,kBAAiB;AACxB,SACEC,cAAc,QAET,mCAAkC;AAOzC,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,kEAAkE;IAClE,OAAQC,MAAMC,IAAI;QAChB,KAAKN;QACL,KAAKC;YAA4B;gBAC/B,OAAO,AAAGI,MAAME,MAAM,CAACC,IAAI,GAAC,OAAIH,MAAME,MAAM,CAACE,OAAO,GAAC,OAAIJ,MAAME,MAAM,CAACG,KAAK;YAC7E;IACF;AACF;AAWA,OAAO,MAAMC,cAAc,CAACC;IAC1B,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,eAAe,CAAC,CAACD,MAAME,UAAU;IAEvC,IAAID,cAAc;QAChB,qBAAO,KAACE;YAAkB,GAAGJ,KAAK;;IACpC,OAAO;QACL,qBAAO,KAACK;YAAoB,GAAGL,KAAK;;IACtC;AACF,EAAC;AAED,MAAMK,qBAAqB;QAAC,EAAEC,QAAQ,EAAEL,KAAK,EAAEM,QAAQ,EAAS;IAC9D,MAAM,EAAEC,MAAM,EAAE,GAAGP;IAEnB,MAAM,CAACQ,SAASC,WAAW,GAAGxB,SAE3B,CAAC;IAEJ,MAAM,CAACyB,eAAeC,UAAU,GAAG3B,QAEjC;QACA,IAAI4B,QAA6B,EAAE;QACnC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMP,OAAOQ,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAIT,MAAM,CAACO,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMT,SAAS;gBACjBI,MAAMM,IAAI,CAACV,OAAO,CAACS,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOZ,MAAM,CAACO,MAAM,EAAE;gBAC5B,IAAIxB,kBAAkB6B,UAAU7B,kBAAkB0B,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACN;QAAQC;KAAQ;IAEpBtB,UAAU;QACR,IAAIyB,aAAa,MAAM;YACrB;QACF;QAEA,IAAIS,UAAU;QAEd/B,eAAesB,WAAWL,UAAUe,IAAI,CAAC,CAACC;YACxC,IAAIF,SAAS;gBACX,sEAAsE;gBACtE,uEAAuE;gBACvE,kBAAkB;gBAClBX,WAAW,CAACc,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAASL,EAAE,CAAC,EAAEK;oBAAS,CAAA;YACrD;QACF;QAEA,OAAO;YACLF,UAAU;QACZ;IACF,GAAG;QAACT;QAAWL;KAAS;IAExB,MAAMkB,kBAAkBjB,OAAOkB,MAAM,CAAC,CAACC,KAAKZ;QAC1C,MAAMK,OAAOZ,MAAM,CAACO,MAAM,EAAE;QAC5B,uBAAuB;QACvB,IAAIA,MAAM,GAAG,OAAOxB,kBAAkB6B,UAAU7B,kBAAkBoC;QAClE,OAAO;IACT,GAAGX,MAAM;IAET,OAAOV,SAAS;QAAEK;QAAec;IAAgB;AACnD;AAEA,MAAMrB,mBAAmB;QAAC,EAAEE,QAAQ,EAAS;IAC3C,OAAOA,SAAS;QACdK,eAAe,EAAE;QACjB,iEAAiE;QACjE,yCAAyC;QACzCc,iBAAiB;IACnB;AACF"}