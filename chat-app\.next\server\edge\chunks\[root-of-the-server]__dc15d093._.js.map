{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from \"next-auth/middleware\"\n\nexport default withAuth(\n  function middleware(req) {\n    // Add any additional middleware logic here\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Protect /chat routes\n        if (req.nextUrl.pathname.startsWith(\"/chat\")) {\n          return !!token\n        }\n        return true\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: [\"/chat/:path*\"]\n}\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;AACrB,2CAA2C;AAC7C,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,uBAAuB;YACvB,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;gBAC5C,OAAO,CAAC,CAAC;YACX;YACA,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QAAC;KAAe;AAC3B"}}]}