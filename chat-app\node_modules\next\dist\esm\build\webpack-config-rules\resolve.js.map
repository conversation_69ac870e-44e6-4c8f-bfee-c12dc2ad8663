{"version": 3, "sources": ["../../../src/build/webpack-config-rules/resolve.ts"], "sourcesContent": ["import {\n  COMPILER_NAMES,\n  type CompilerNameValues,\n} from '../../shared/lib/constants'\n\n// exports.<conditionName>\nexport const edgeConditionNames = [\n  'edge-light',\n  // inherits the default conditions\n  '...',\n]\n\nconst mainFieldsPerCompiler = {\n  // For default case, prefer CJS over ESM on server side. e.g. pages dir SSR\n  [COMPILER_NAMES.server]: ['main', 'module'],\n  [COMPILER_NAMES.client]: ['browser', 'module', 'main'],\n  // For bundling-all strategy, prefer ESM over CJS\n  'server-esm': ['module', 'main'],\n}\n\nexport function getMainField(\n  compilerType: CompilerNameValues,\n  preferEsm: boolean\n) {\n  if (compilerType === COMPILER_NAMES.edgeServer) {\n    return edgeConditionNames\n  } else if (compilerType === COMPILER_NAMES.client) {\n    return mainFieldsPerCompiler[COMPILER_NAMES.client]\n  }\n\n  // Prefer module fields over main fields for isomorphic packages on server layer\n  return preferEsm\n    ? mainFieldsPerCompiler['server-esm']\n    : mainFieldsPerCompiler[COMPILER_NAMES.server]\n}\n"], "names": ["COMPILER_NAMES", "edgeConditionNames", "mainFieldsPerCompiler", "server", "client", "getMainField", "compilerType", "preferEsm", "edgeServer"], "mappings": "AAAA,SACEA,cAAc,QAET,6BAA4B;AAEnC,0BAA0B;AAC1B,OAAO,MAAMC,qBAAqB;IAChC;IACA,kCAAkC;IAClC;CACD,CAAA;AAED,MAAMC,wBAAwB;IAC5B,2EAA2E;IAC3E,CAACF,eAAeG,MAAM,CAAC,EAAE;QAAC;QAAQ;KAAS;IAC3C,CAACH,eAAeI,MAAM,CAAC,EAAE;QAAC;QAAW;QAAU;KAAO;IACtD,iDAAiD;IACjD,cAAc;QAAC;QAAU;KAAO;AAClC;AAEA,OAAO,SAASC,aACdC,YAAgC,EAChCC,SAAkB;IAElB,IAAID,iBAAiBN,eAAeQ,UAAU,EAAE;QAC9C,OAAOP;IACT,OAAO,IAAIK,iBAAiBN,eAAeI,MAAM,EAAE;QACjD,OAAOF,qBAAqB,CAACF,eAAeI,MAAM,CAAC;IACrD;IAEA,gFAAgF;IAChF,OAAOG,YACHL,qBAAqB,CAAC,aAAa,GACnCA,qBAAqB,CAACF,eAAeG,MAAM,CAAC;AAClD"}