{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Flowise/agent/chat-app/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\"\nimport bcrypt from \"bcryptjs\"\nimport { prisma } from \"@/lib/prisma\"\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { name, email, password } = await request.json()\n\n    if (!name || !email || !password) {\n      return NextResponse.json(\n        { error: \"Missing required fields\" },\n        { status: 400 }\n      )\n    }\n\n    // Check if user already exists\n    const existingUser = await prisma.user.findUnique({\n      where: { email }\n    })\n\n    if (existingUser) {\n      return NextResponse.json(\n        { error: \"User already exists\" },\n        { status: 400 }\n      )\n    }\n\n    // Hash password\n    const hashedPassword = await bcrypt.hash(password, 12)\n\n    // Create user\n    const user = await prisma.user.create({\n      data: {\n        name,\n        email,\n        password: hashedPassword\n      }\n    })\n\n    // Remove password from response\n    const { password: _, ...userWithoutPassword } = user\n\n    return NextResponse.json(\n      { message: \"User created successfully\", user: userWithoutPassword },\n      { status: 201 }\n    )\n  } catch (error) {\n    console.error(\"Registration error:\", error)\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,cAAc;QACd,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,UAAU;YACZ;QACF;QAEA,gCAAgC;QAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAA6B,MAAM;QAAoB,GAClE;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}