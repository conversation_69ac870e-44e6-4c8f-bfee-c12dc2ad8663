{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/contexts/server-inserted-metadata.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedMetadata\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedMetadata"], "mappings": "AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,WACD,CAACC,sBAAsB"}