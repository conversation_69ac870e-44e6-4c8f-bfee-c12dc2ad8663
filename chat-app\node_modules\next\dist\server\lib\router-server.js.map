{"version": 3, "sources": ["../../../src/server/lib/router-server.ts"], "sourcesContent": ["// this must come first as it includes require hooks\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './types'\nimport type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Fields } from './router-utils/setup-dev-bundler'\nimport type { NextUrlWithParsedQuery, RequestMeta } from '../request-meta'\n\n// This is required before other imports to ensure the require hook is setup.\nimport '../node-environment'\nimport '../require-hook'\n\nimport url from 'url'\nimport path from 'path'\nimport loadConfig from '../config'\nimport { serveStatic } from '../serve-static'\nimport setupDebug from 'next/dist/compiled/debug'\nimport * as Log from '../../build/output/log'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport { setupFsCheck } from './router-utils/filesystem'\nimport { proxyRequest } from './router-utils/proxy-request'\nimport { isAbortError, pipeToNodeResponse } from '../pipe-readable'\nimport { getResolveRoutes } from './router-utils/resolve-routes'\nimport { addRequestMeta, getRequestMeta } from '../request-meta'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport setupCompression from 'next/dist/compiled/compression'\nimport { NoFallbackError } from '../base-server'\nimport { signalFromNodeResponse } from '../web/spec-extension/adapters/next-request'\nimport { isPostpone } from './router-utils/is-postpone'\nimport { parseUrl as parseUrlUtil } from '../../shared/lib/router/utils/parse-url'\n\nimport {\n  PHASE_PRODUCTION_SERVER,\n  PHASE_DEVELOPMENT_SERVER,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../../shared/lib/constants'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { DevBundlerService } from './dev-bundler-service'\nimport { type Span, trace } from '../../trace'\nimport { ensureLeadingSlash } from '../../shared/lib/page-path/ensure-leading-slash'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { MockedResponse } from './mock-request'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type AppIsrManifestAction,\n} from '../dev/hot-reloader-types'\nimport { normalizedAssetPrefix } from '../../shared/lib/normalized-asset-prefix'\nimport { NEXT_PATCH_SYMBOL } from './patch-fetch'\nimport type { ServerInitResult } from './render-server'\nimport { filterInternalHeaders } from './server-ipc/utils'\nimport { blockCrossSite } from './router-utils/block-cross-site'\nimport { traceGlobals } from '../../trace/shared'\n\nconst debug = setupDebug('next:router-server:main')\nconst isNextFont = (pathname: string | null) =>\n  pathname && /\\/media\\/[^/]+\\.(woff|woff2|eot|ttf|otf)$/.test(pathname)\n\nexport type RenderServer = Pick<\n  typeof import('./render-server'),\n  | 'initialize'\n  | 'clearModuleContext'\n  | 'propagateServerField'\n  | 'getServerField'\n>\n\nexport interface LazyRenderServerInstance {\n  instance?: RenderServer\n}\n\nconst requestHandlers: Record<string, WorkerRequestHandler> = {}\n\nexport async function initialize(opts: {\n  dir: string\n  port: number\n  dev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  minimalMode?: boolean\n  hostname?: string\n  keepAliveTimeout?: number\n  customServer?: boolean\n  experimentalHttpsServer?: boolean\n  startServerSpan?: Span\n  quiet?: boolean\n}): Promise<ServerInitResult> {\n  if (!process.env.NODE_ENV) {\n    // @ts-ignore not readonly\n    process.env.NODE_ENV = opts.dev ? 'development' : 'production'\n  }\n\n  const config = await loadConfig(\n    opts.dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_SERVER,\n    opts.dir,\n    { silent: false }\n  )\n\n  let compress: ReturnType<typeof setupCompression> | undefined\n\n  if (config?.compress !== false) {\n    compress = setupCompression()\n  }\n\n  const fsChecker = await setupFsCheck({\n    dev: opts.dev,\n    dir: opts.dir,\n    config,\n    minimalMode: opts.minimalMode,\n  })\n\n  const renderServer: LazyRenderServerInstance = {}\n\n  let developmentBundler: DevBundler | undefined\n\n  let devBundlerService: DevBundlerService | undefined\n\n  let originalFetch = globalThis.fetch\n\n  if (opts.dev) {\n    const { Telemetry } =\n      require('../../telemetry/storage') as typeof import('../../telemetry/storage')\n\n    const telemetry = new Telemetry({\n      distDir: path.join(opts.dir, config.distDir),\n    })\n    traceGlobals.set('telemetry', telemetry)\n\n    const { pagesDir, appDir } = findPagesDir(opts.dir)\n\n    const { setupDevBundler } =\n      require('./router-utils/setup-dev-bundler') as typeof import('./router-utils/setup-dev-bundler')\n\n    const resetFetch = () => {\n      globalThis.fetch = originalFetch\n      ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = false\n    }\n\n    const setupDevBundlerSpan = opts.startServerSpan\n      ? opts.startServerSpan.traceChild('setup-dev-bundler')\n      : trace('setup-dev-bundler')\n    developmentBundler = await setupDevBundlerSpan.traceAsyncFn(() =>\n      setupDevBundler({\n        // Passed here but the initialization of this object happens below, doing the initialization before the setupDev call breaks.\n        renderServer,\n        appDir,\n        pagesDir,\n        telemetry,\n        fsChecker,\n        dir: opts.dir,\n        nextConfig: config,\n        isCustomServer: opts.customServer,\n        turbo: !!process.env.TURBOPACK,\n        port: opts.port,\n        onDevServerCleanup: opts.onDevServerCleanup,\n        resetFetch,\n      })\n    )\n\n    devBundlerService = new DevBundlerService(\n      developmentBundler,\n      // The request handler is assigned below, this allows us to create a lazy\n      // reference to it.\n      (req, res) => {\n        return requestHandlers[opts.dir](req, res)\n      }\n    )\n  }\n\n  renderServer.instance =\n    require('./render-server') as typeof import('./render-server')\n\n  const requestHandlerImpl: WorkerRequestHandler = async (req, res) => {\n    // internal headers should not be honored by the request handler\n    if (!process.env.NEXT_PRIVATE_TEST_HEADERS) {\n      filterInternalHeaders(req.headers)\n    }\n\n    if (\n      !opts.minimalMode &&\n      config.i18n &&\n      config.i18n.localeDetection !== false\n    ) {\n      const urlParts = (req.url || '').split('?', 1)\n      let urlNoQuery = urlParts[0] || ''\n\n      if (config.basePath) {\n        urlNoQuery = removePathPrefix(urlNoQuery, config.basePath)\n      }\n\n      const pathnameInfo = getNextPathnameInfo(urlNoQuery, {\n        nextConfig: config,\n      })\n\n      const domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname({ hostname: urlNoQuery }, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      const { getLocaleRedirect } =\n        require('../../shared/lib/i18n/get-locale-redirect') as typeof import('../../shared/lib/i18n/get-locale-redirect')\n\n      const parsedUrl = parseUrlUtil((req.url || '')?.replace(/^\\/+/, '/'))\n\n      const redirect = getLocaleRedirect({\n        defaultLocale,\n        domainLocale,\n        headers: req.headers,\n        nextConfig: config,\n        pathLocale: pathnameInfo.locale,\n        urlParsed: {\n          ...parsedUrl,\n          pathname: pathnameInfo.locale\n            ? `/${pathnameInfo.locale}${urlNoQuery}`\n            : urlNoQuery,\n        },\n      })\n\n      if (redirect) {\n        res.setHeader('Location', redirect)\n        res.statusCode = RedirectStatusCode.TemporaryRedirect\n        res.end(redirect)\n        return\n      }\n    }\n\n    if (compress) {\n      // @ts-expect-error not express req/res\n      compress(req, res, () => {})\n    }\n    req.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n    res.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n\n    const invokedOutputs = new Set<string>()\n\n    async function invokeRender(\n      parsedUrl: NextUrlWithParsedQuery,\n      invokePath: string,\n      handleIndex: number,\n      additionalRequestMeta?: RequestMeta\n    ) {\n      // invokeRender expects /api routes to not be locale prefixed\n      // so normalize here before continuing\n      if (\n        config.i18n &&\n        removePathPrefix(invokePath, config.basePath).startsWith(\n          `/${getRequestMeta(req, 'locale')}/api`\n        )\n      ) {\n        invokePath = fsChecker.handleLocale(\n          removePathPrefix(invokePath, config.basePath)\n        ).pathname\n      }\n\n      if (\n        req.headers['x-nextjs-data'] &&\n        fsChecker.getMiddlewareMatchers()?.length &&\n        removePathPrefix(invokePath, config.basePath) === '/404'\n      ) {\n        res.setHeader('x-nextjs-matched-path', parsedUrl.pathname || '')\n        res.statusCode = 404\n        res.setHeader('content-type', 'application/json')\n        res.end('{}')\n        return null\n      }\n\n      if (!handlers) {\n        throw new Error('Failed to initialize render server')\n      }\n\n      addRequestMeta(req, 'invokePath', invokePath)\n      addRequestMeta(req, 'invokeQuery', parsedUrl.query)\n      addRequestMeta(req, 'middlewareInvoke', false)\n\n      for (const key in additionalRequestMeta || {}) {\n        addRequestMeta(\n          req,\n          key as keyof RequestMeta,\n          additionalRequestMeta![key as keyof RequestMeta]\n        )\n      }\n\n      debug('invokeRender', req.url, req.headers)\n\n      try {\n        const initResult =\n          await renderServer?.instance?.initialize(renderServerOpts)\n        try {\n          await initResult?.requestHandler(req, res)\n        } catch (err) {\n          if (err instanceof NoFallbackError) {\n            // eslint-disable-next-line\n            await handleRequest(handleIndex + 1)\n            return\n          }\n          throw err\n        }\n        return\n      } catch (e) {\n        // If the client aborts before we can receive a response object (when\n        // the headers are flushed), then we can early exit without further\n        // processing.\n        if (isAbortError(e)) {\n          return\n        }\n        throw e\n      }\n    }\n\n    const handleRequest = async (handleIndex: number) => {\n      if (handleIndex > 5) {\n        throw new Error(`Attempted to handle request too many times ${req.url}`)\n      }\n\n      // handle hot-reloader first\n      if (developmentBundler) {\n        if (blockCrossSite(req, res, config.allowedDevOrigins, opts.hostname)) {\n          return\n        }\n        const origUrl = req.url || '/'\n\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        }\n        const parsedUrl = url.parse(req.url || '/')\n\n        const hotReloaderResult = await developmentBundler.hotReloader.run(\n          req,\n          res,\n          parsedUrl\n        )\n\n        if (hotReloaderResult.finished) {\n          return hotReloaderResult\n        }\n        req.url = origUrl\n      }\n\n      const {\n        finished,\n        parsedUrl,\n        statusCode,\n        resHeaders,\n        bodyStream,\n        matchedOutput,\n      } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: false,\n        signal: signalFromNodeResponse(res),\n        invokedOutputs,\n      })\n\n      if (res.closed || res.finished) {\n        return\n      }\n\n      if (developmentBundler && matchedOutput?.type === 'devVirtualFsItem') {\n        const origUrl = req.url || '/'\n\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        }\n\n        if (resHeaders) {\n          for (const key of Object.keys(resHeaders)) {\n            res.setHeader(key, resHeaders[key])\n          }\n        }\n        const result = await developmentBundler.requestHandler(req, res)\n\n        if (result.finished) {\n          return\n        }\n        // TODO: throw invariant if we resolved to this but it wasn't handled?\n        req.url = origUrl\n      }\n\n      debug('requestHandler!', req.url, {\n        matchedOutput,\n        statusCode,\n        resHeaders,\n        bodyStream: !!bodyStream,\n        parsedUrl: {\n          pathname: parsedUrl.pathname,\n          query: parsedUrl.query,\n        },\n        finished,\n      })\n\n      // apply any response headers from routing\n      for (const key of Object.keys(resHeaders || {})) {\n        res.setHeader(key, resHeaders[key])\n      }\n\n      // handle redirect\n      if (!bodyStream && statusCode && statusCode > 300 && statusCode < 400) {\n        const destination = url.format(parsedUrl)\n        res.statusCode = statusCode\n        res.setHeader('location', destination)\n\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n          res.setHeader('Refresh', `0;url=${destination}`)\n        }\n        return res.end(destination)\n      }\n\n      // handle middleware body response\n      if (bodyStream) {\n        res.statusCode = statusCode || 200\n        return await pipeToNodeResponse(bodyStream, res)\n      }\n\n      if (finished && parsedUrl.protocol) {\n        return await proxyRequest(\n          req,\n          res,\n          parsedUrl,\n          undefined,\n          getRequestMeta(req, 'clonableBody')?.cloneBodyStream(),\n          config.experimental.proxyTimeout\n        )\n      }\n\n      if (matchedOutput?.fsPath && matchedOutput.itemPath) {\n        if (\n          opts.dev &&\n          (fsChecker.appFiles.has(matchedOutput.itemPath) ||\n            fsChecker.pageFiles.has(matchedOutput.itemPath))\n        ) {\n          res.statusCode = 500\n          const message = `A conflicting public file and page file was found for path ${matchedOutput.itemPath} https://nextjs.org/docs/messages/conflicting-public-file-page`\n          await invokeRender(parsedUrl, '/_error', handleIndex, {\n            invokeStatus: 500,\n            invokeError: new Error(message),\n          })\n          Log.error(message)\n          return\n        }\n\n        if (\n          !res.getHeader('cache-control') &&\n          matchedOutput.type === 'nextStaticFolder'\n        ) {\n          if (opts.dev && !isNextFont(parsedUrl.pathname)) {\n            res.setHeader('Cache-Control', 'no-store, must-revalidate')\n          } else {\n            res.setHeader(\n              'Cache-Control',\n              'public, max-age=31536000, immutable'\n            )\n          }\n        }\n        if (!(req.method === 'GET' || req.method === 'HEAD')) {\n          res.setHeader('Allow', ['GET', 'HEAD'])\n          res.statusCode = 405\n          return await invokeRender(\n            url.parse('/405', true),\n            '/405',\n            handleIndex,\n            {\n              invokeStatus: 405,\n            }\n          )\n        }\n\n        try {\n          return await serveStatic(req, res, matchedOutput.itemPath, {\n            root: matchedOutput.itemsRoot,\n            // Ensures that etags are not generated for static files when disabled.\n            etag: config.generateEtags,\n          })\n        } catch (err: any) {\n          /**\n           * Hardcoded every possible error status code that could be thrown by \"serveStatic\" method\n           * This is done by searching \"this.error\" inside \"send\" module's source code:\n           * https://github.com/pillarjs/send/blob/master/index.js\n           * https://github.com/pillarjs/send/blob/develop/index.js\n           */\n          const POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC = new Set([\n            // send module will throw 500 when header is already sent or fs.stat error happens\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L392\n            // Note: we will use Next.js built-in 500 page to handle 500 errors\n            // 500,\n\n            // send module will throw 404 when file is missing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L421\n            // Note: we will use Next.js built-in 404 page to handle 404 errors\n            // 404,\n\n            // send module will throw 403 when redirecting to a directory without enabling directory listing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L484\n            // Note: Next.js throws a different error (without status code) for directory listing\n            // 403,\n\n            // send module will throw 400 when fails to normalize the path\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L520\n            400,\n\n            // send module will throw 412 with conditional GET request\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L632\n            412,\n\n            // send module will throw 416 when range is not satisfiable\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L669\n            416,\n          ])\n\n          let validErrorStatus = POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC.has(\n            err.statusCode\n          )\n\n          // normalize non-allowed status codes\n          if (!validErrorStatus) {\n            ;(err as any).statusCode = 400\n          }\n\n          if (typeof err.statusCode === 'number') {\n            const invokePath = `/${err.statusCode}`\n            const invokeStatus = err.statusCode\n            res.statusCode = err.statusCode\n            return await invokeRender(\n              url.parse(invokePath, true),\n              invokePath,\n              handleIndex,\n              {\n                invokeStatus,\n              }\n            )\n          }\n          throw err\n        }\n      }\n\n      if (matchedOutput) {\n        invokedOutputs.add(matchedOutput.itemPath)\n\n        return await invokeRender(\n          parsedUrl,\n          parsedUrl.pathname || '/',\n          handleIndex,\n          {\n            invokeOutput: matchedOutput.itemPath,\n          }\n        )\n      }\n\n      // 404 case\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n\n      // Short-circuit favicon.ico serving so that the 404 page doesn't get built as favicon is requested by the browser when loading any route.\n      if (opts.dev && !matchedOutput && parsedUrl.pathname === '/favicon.ico') {\n        res.statusCode = 404\n        res.end('')\n        return null\n      }\n\n      const appNotFound = opts.dev\n        ? developmentBundler?.serverFields.hasAppNotFound\n        : await fsChecker.getItem(UNDERSCORE_NOT_FOUND_ROUTE)\n\n      res.statusCode = 404\n\n      if (appNotFound) {\n        return await invokeRender(\n          parsedUrl,\n          UNDERSCORE_NOT_FOUND_ROUTE,\n          handleIndex,\n          {\n            invokeStatus: 404,\n          }\n        )\n      }\n\n      await invokeRender(parsedUrl, '/404', handleIndex, {\n        invokeStatus: 404,\n      })\n    }\n\n    try {\n      await handleRequest(0)\n    } catch (err) {\n      try {\n        let invokePath = '/500'\n        let invokeStatus = '500'\n\n        if (err instanceof DecodeError) {\n          invokePath = '/400'\n          invokeStatus = '400'\n        } else {\n          console.error(err)\n        }\n        res.statusCode = Number(invokeStatus)\n        return await invokeRender(url.parse(invokePath, true), invokePath, 0, {\n          invokeStatus: res.statusCode,\n        })\n      } catch (err2) {\n        console.error(err2)\n      }\n      res.statusCode = 500\n      res.end('Internal Server Error')\n    }\n  }\n\n  let requestHandler: WorkerRequestHandler = requestHandlerImpl\n  if (config.experimental.testProxy) {\n    // Intercept fetch and other testmode apis.\n    const { wrapRequestHandlerWorker, interceptTestApis } =\n      require('next/dist/experimental/testmode/server') as typeof import('next/src/experimental/testmode/server')\n    requestHandler = wrapRequestHandlerWorker(requestHandler)\n    interceptTestApis()\n    // We treat the intercepted fetch as \"original\" fetch that should be reset to during HMR.\n    originalFetch = globalThis.fetch\n  }\n  requestHandlers[opts.dir] = requestHandler\n\n  const renderServerOpts: Parameters<RenderServer['initialize']>[0] = {\n    port: opts.port,\n    dir: opts.dir,\n    hostname: opts.hostname,\n    minimalMode: opts.minimalMode,\n    dev: !!opts.dev,\n    server: opts.server,\n    serverFields: {\n      ...(developmentBundler?.serverFields || {}),\n      setIsrStatus: devBundlerService?.setIsrStatus.bind(devBundlerService),\n    } satisfies ServerFields,\n    experimentalTestProxy: !!config.experimental.testProxy,\n    experimentalHttpsServer: !!opts.experimentalHttpsServer,\n    bundlerService: devBundlerService,\n    startServerSpan: opts.startServerSpan,\n    quiet: opts.quiet,\n    onDevServerCleanup: opts.onDevServerCleanup,\n  }\n  renderServerOpts.serverFields.routerServerHandler = requestHandlerImpl\n\n  // pre-initialize workers\n  const handlers = await renderServer.instance.initialize(renderServerOpts)\n\n  const logError = async (\n    type: 'uncaughtException' | 'unhandledRejection',\n    err: Error | undefined\n  ) => {\n    if (isPostpone(err)) {\n      // React postpones that are unhandled might end up logged here but they're\n      // not really errors. They're just part of rendering.\n      return\n    }\n    if (type === 'unhandledRejection') {\n      Log.error('unhandledRejection: ', err)\n    } else if (type === 'uncaughtException') {\n      Log.error('uncaughtException: ', err)\n    }\n  }\n\n  process.on('uncaughtException', logError.bind(null, 'uncaughtException'))\n  process.on('unhandledRejection', logError.bind(null, 'unhandledRejection'))\n\n  const resolveRoutes = getResolveRoutes(\n    fsChecker,\n    config,\n    opts,\n    renderServer.instance,\n    renderServerOpts,\n    developmentBundler?.ensureMiddleware\n  )\n\n  const upgradeHandler: WorkerUpgradeHandler = async (req, socket, head) => {\n    try {\n      req.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n      socket.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n\n      if (opts.dev && developmentBundler && req.url) {\n        if (\n          blockCrossSite(req, socket, config.allowedDevOrigins, opts.hostname)\n        ) {\n          return\n        }\n        const { basePath, assetPrefix } = config\n\n        let hmrPrefix = basePath\n\n        // assetPrefix overrides basePath for HMR path\n        if (assetPrefix) {\n          hmrPrefix = normalizedAssetPrefix(assetPrefix)\n\n          if (URL.canParse(hmrPrefix)) {\n            // remove trailing slash from pathname\n            // return empty string if pathname is '/'\n            // to avoid conflicts with '/_next' below\n            hmrPrefix = new URL(hmrPrefix).pathname.replace(/\\/$/, '')\n          }\n        }\n\n        const isHMRRequest = req.url.startsWith(\n          ensureLeadingSlash(`${hmrPrefix}/_next/webpack-hmr`)\n        )\n\n        // only handle HMR requests if the basePath in the request\n        // matches the basePath for the handler responding to the request\n        if (isHMRRequest) {\n          return developmentBundler.hotReloader.onHMR(\n            req,\n            socket,\n            head,\n            (client) => {\n              client.send(\n                JSON.stringify({\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST,\n                  data: devBundlerService?.appIsrManifest || {},\n                } satisfies AppIsrManifestAction)\n              )\n            }\n          )\n        }\n      }\n\n      const res = new MockedResponse({\n        resWriter: () => {\n          throw new Error(\n            'Invariant: did not expect response writer to be written to for upgrade request'\n          )\n        },\n      })\n      const { matchedOutput, parsedUrl } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: true,\n        signal: signalFromNodeResponse(socket),\n      })\n\n      // TODO: allow upgrade requests to pages/app paths?\n      // this was not previously supported\n      if (matchedOutput) {\n        return socket.end()\n      }\n\n      if (parsedUrl.protocol) {\n        return await proxyRequest(req, socket, parsedUrl, head)\n      }\n\n      // If there's no matched output, we don't handle the request as user's\n      // custom WS server may be listening on the same path.\n    } catch (err) {\n      console.error('Error handling upgrade request', err)\n      socket.end()\n    }\n  }\n\n  return {\n    requestHandler,\n    upgradeHandler,\n    server: handlers.server,\n    closeUpgraded() {\n      developmentBundler?.hotReloader?.close()\n    },\n  }\n}\n"], "names": ["initialize", "debug", "setupDebug", "isNextFont", "pathname", "test", "requestHandlers", "opts", "process", "env", "NODE_ENV", "dev", "config", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_SERVER", "dir", "silent", "compress", "setupCompression", "fs<PERSON><PERSON><PERSON>", "setupFsCheck", "minimalMode", "renderServer", "developmentBundler", "devBundlerService", "originalFetch", "globalThis", "fetch", "Telemetry", "require", "telemetry", "distDir", "path", "join", "traceGlobals", "set", "pagesDir", "appDir", "findPagesDir", "setupDevBundler", "resetFetch", "NEXT_PATCH_SYMBOL", "setupDevBundlerSpan", "startServerSpan", "<PERSON><PERSON><PERSON><PERSON>", "trace", "traceAsyncFn", "nextConfig", "isCustomServer", "customServer", "turbo", "TURBOPACK", "port", "onDevServerCleanup", "DevBundlerService", "req", "res", "instance", "requestHandlerImpl", "NEXT_PRIVATE_TEST_HEADERS", "filterInternalHeaders", "headers", "i18n", "localeDetection", "urlParts", "url", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basePath", "removePathPrefix", "pathnameInfo", "getNextPathnameInfo", "domainLocale", "detectDomainLocale", "domains", "getHostname", "hostname", "defaultLocale", "getLocaleRedirect", "parsedUrl", "parseUrlUtil", "replace", "redirect", "pathLocale", "locale", "urlParsed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "end", "on", "_err", "invokedOutputs", "Set", "invokeRender", "invoke<PERSON><PERSON>", "handleIndex", "additionalRequestMeta", "startsWith", "getRequestMeta", "handleLocale", "getMiddlewareMatchers", "length", "handlers", "Error", "addRequestMeta", "query", "key", "initResult", "renderServerOpts", "requestHandler", "err", "NoFallbackError", "handleRequest", "e", "isAbortError", "blockCrossSite", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "origUrl", "pathHasPrefix", "parse", "hotReloaderResult", "hotReloader", "run", "finished", "resHeaders", "bodyStream", "matchedOutput", "resolveRoutes", "isUpgradeReq", "signal", "signalFromNodeResponse", "closed", "type", "Object", "keys", "result", "destination", "format", "PermanentRedirect", "pipeToNodeResponse", "protocol", "proxyRequest", "undefined", "cloneBodyStream", "experimental", "proxyTimeout", "fsPath", "itemPath", "appFiles", "has", "pageFiles", "message", "invoke<PERSON>tatus", "invokeError", "Log", "error", "<PERSON><PERSON><PERSON><PERSON>", "method", "serveStatic", "root", "itemsRoot", "etag", "generateEtags", "POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC", "validErrorStatus", "add", "invokeOutput", "appNotFound", "serverFields", "hasAppNotFound", "getItem", "UNDERSCORE_NOT_FOUND_ROUTE", "DecodeError", "console", "Number", "err2", "testProxy", "wrapRequestHandlerWorker", "interceptTestApis", "server", "setIsrStatus", "bind", "experimentalTestProxy", "experimentalHttpsServer", "bundlerService", "quiet", "routerServerHandler", "logError", "isPostpone", "getResolveRoutes", "ensureMiddleware", "upgradeHandler", "socket", "head", "assetPrefix", "hmrPrefix", "normalizedAssetPrefix", "URL", "canParse", "isHMRRequest", "ensureLeadingSlash", "onHMR", "client", "send", "JSON", "stringify", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "data", "appIsrManifest", "MockedResponse", "resWriter", "closeUpgraded", "close"], "mappings": "AAAA,oDAAoD;;;;;+BAwE9BA;;;eAAAA;;;QAlEf;QACA;4DAES;6DACC;+DACM;6BACK;8DACL;6DACF;uBACO;8BACC;4BACA;8BACA;8BACoB;+BAChB;6BACc;+BACjB;kCACG;oEACJ;4BACG;6BACO;4BACZ;0BACc;2BAMlC;oCAC4B;mCACD;uBACD;oCACE;qCACC;6BACR;oCACO;6BACJ;kCAIxB;uCAC+B;4BACJ;wBAEI;gCACP;wBACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AACzB,MAAMC,aAAa,CAACC,WAClBA,YAAY,4CAA4CC,IAAI,CAACD;AAc/D,MAAME,kBAAwD,CAAC;AAExD,eAAeN,WAAWO,IAahC;IACC,IAAI,CAACC,QAAQC,GAAG,CAACC,QAAQ,EAAE;QACzB,0BAA0B;QAC1BF,QAAQC,GAAG,CAACC,QAAQ,GAAGH,KAAKI,GAAG,GAAG,gBAAgB;IACpD;IAEA,MAAMC,SAAS,MAAMC,IAAAA,eAAU,EAC7BN,KAAKI,GAAG,GAAGG,mCAAwB,GAAGC,kCAAuB,EAC7DR,KAAKS,GAAG,EACR;QAAEC,QAAQ;IAAM;IAGlB,IAAIC;IAEJ,IAAIN,CAAAA,0BAAAA,OAAQM,QAAQ,MAAK,OAAO;QAC9BA,WAAWC,IAAAA,oBAAgB;IAC7B;IAEA,MAAMC,YAAY,MAAMC,IAAAA,wBAAY,EAAC;QACnCV,KAAKJ,KAAKI,GAAG;QACbK,KAAKT,KAAKS,GAAG;QACbJ;QACAU,aAAaf,KAAKe,WAAW;IAC/B;IAEA,MAAMC,eAAyC,CAAC;IAEhD,IAAIC;IAEJ,IAAIC;IAEJ,IAAIC,gBAAgBC,WAAWC,KAAK;IAEpC,IAAIrB,KAAKI,GAAG,EAAE;QACZ,MAAM,EAAEkB,SAAS,EAAE,GACjBC,QAAQ;QAEV,MAAMC,YAAY,IAAIF,UAAU;YAC9BG,SAASC,aAAI,CAACC,IAAI,CAAC3B,KAAKS,GAAG,EAAEJ,OAAOoB,OAAO;QAC7C;QACAG,oBAAY,CAACC,GAAG,CAAC,aAAaL;QAE9B,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAChC,KAAKS,GAAG;QAElD,MAAM,EAAEwB,eAAe,EAAE,GACvBV,QAAQ;QAEV,MAAMW,aAAa;YACjBd,WAAWC,KAAK,GAAGF;YACjBC,UAAsC,CAACe,6BAAiB,CAAC,GAAG;QAChE;QAEA,MAAMC,sBAAsBpC,KAAKqC,eAAe,GAC5CrC,KAAKqC,eAAe,CAACC,UAAU,CAAC,uBAChCC,IAAAA,YAAK,EAAC;QACVtB,qBAAqB,MAAMmB,oBAAoBI,YAAY,CAAC,IAC1DP,gBAAgB;gBACd,6HAA6H;gBAC7HjB;gBACAe;gBACAD;gBACAN;gBACAX;gBACAJ,KAAKT,KAAKS,GAAG;gBACbgC,YAAYpC;gBACZqC,gBAAgB1C,KAAK2C,YAAY;gBACjCC,OAAO,CAAC,CAAC3C,QAAQC,GAAG,CAAC2C,SAAS;gBAC9BC,MAAM9C,KAAK8C,IAAI;gBACfC,oBAAoB/C,KAAK+C,kBAAkB;gBAC3Cb;YACF;QAGFhB,oBAAoB,IAAI8B,oCAAiB,CACvC/B,oBACA,yEAAyE;QACzE,mBAAmB;QACnB,CAACgC,KAAKC;YACJ,OAAOnD,eAAe,CAACC,KAAKS,GAAG,CAAC,CAACwC,KAAKC;QACxC;IAEJ;IAEAlC,aAAamC,QAAQ,GACnB5B,QAAQ;IAEV,MAAM6B,qBAA2C,OAAOH,KAAKC;QAC3D,gEAAgE;QAChE,IAAI,CAACjD,QAAQC,GAAG,CAACmD,yBAAyB,EAAE;YAC1CC,IAAAA,6BAAqB,EAACL,IAAIM,OAAO;QACnC;QAEA,IACE,CAACvD,KAAKe,WAAW,IACjBV,OAAOmD,IAAI,IACXnD,OAAOmD,IAAI,CAACC,eAAe,KAAK,OAChC;gBAuBgCR;YAtBhC,MAAMS,WAAW,AAACT,CAAAA,IAAIU,GAAG,IAAI,EAAC,EAAGC,KAAK,CAAC,KAAK;YAC5C,IAAIC,aAAaH,QAAQ,CAAC,EAAE,IAAI;YAEhC,IAAIrD,OAAOyD,QAAQ,EAAE;gBACnBD,aAAaE,IAAAA,kCAAgB,EAACF,YAAYxD,OAAOyD,QAAQ;YAC3D;YAEA,MAAME,eAAeC,IAAAA,wCAAmB,EAACJ,YAAY;gBACnDpB,YAAYpC;YACd;YAEA,MAAM6D,eAAeC,IAAAA,sCAAkB,EACrC9D,OAAOmD,IAAI,CAACY,OAAO,EACnBC,IAAAA,wBAAW,EAAC;gBAAEC,UAAUT;YAAW,GAAGZ,IAAIM,OAAO;YAGnD,MAAMgB,gBACJL,CAAAA,gCAAAA,aAAcK,aAAa,KAAIlE,OAAOmD,IAAI,CAACe,aAAa;YAE1D,MAAM,EAAEC,iBAAiB,EAAE,GACzBjD,QAAQ;YAEV,MAAMkD,YAAYC,IAAAA,kBAAY,GAAEzB,QAAAA,IAAIU,GAAG,IAAI,uBAAZ,AAACV,MAAgB0B,OAAO,CAAC,QAAQ;YAEhE,MAAMC,WAAWJ,kBAAkB;gBACjCD;gBACAL;gBACAX,SAASN,IAAIM,OAAO;gBACpBd,YAAYpC;gBACZwE,YAAYb,aAAac,MAAM;gBAC/BC,WAAW;oBACT,GAAGN,SAAS;oBACZ5E,UAAUmE,aAAac,MAAM,GACzB,CAAC,CAAC,EAAEd,aAAac,MAAM,GAAGjB,YAAY,GACtCA;gBACN;YACF;YAEA,IAAIe,UAAU;gBACZ1B,IAAI8B,SAAS,CAAC,YAAYJ;gBAC1B1B,IAAI+B,UAAU,GAAGC,sCAAkB,CAACC,iBAAiB;gBACrDjC,IAAIkC,GAAG,CAACR;gBACR;YACF;QACF;QAEA,IAAIjE,UAAU;YACZ,uCAAuC;YACvCA,SAASsC,KAAKC,KAAK,KAAO;QAC5B;QACAD,IAAIoC,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QACApC,IAAImC,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QAEA,MAAMC,iBAAiB,IAAIC;QAE3B,eAAeC,aACbhB,SAAiC,EACjCiB,UAAkB,EAClBC,WAAmB,EACnBC,qBAAmC;gBAiBjC/E;YAfF,6DAA6D;YAC7D,sCAAsC;YACtC,IACER,OAAOmD,IAAI,IACXO,IAAAA,kCAAgB,EAAC2B,YAAYrF,OAAOyD,QAAQ,EAAE+B,UAAU,CACtD,CAAC,CAAC,EAAEC,IAAAA,2BAAc,EAAC7C,KAAK,UAAU,IAAI,CAAC,GAEzC;gBACAyC,aAAa7E,UAAUkF,YAAY,CACjChC,IAAAA,kCAAgB,EAAC2B,YAAYrF,OAAOyD,QAAQ,GAC5CjE,QAAQ;YACZ;YAEA,IACEoD,IAAIM,OAAO,CAAC,gBAAgB,MAC5B1C,mCAAAA,UAAUmF,qBAAqB,uBAA/BnF,iCAAmCoF,MAAM,KACzClC,IAAAA,kCAAgB,EAAC2B,YAAYrF,OAAOyD,QAAQ,MAAM,QAClD;gBACAZ,IAAI8B,SAAS,CAAC,yBAAyBP,UAAU5E,QAAQ,IAAI;gBAC7DqD,IAAI+B,UAAU,GAAG;gBACjB/B,IAAI8B,SAAS,CAAC,gBAAgB;gBAC9B9B,IAAIkC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,IAAI,CAACc,UAAU;gBACb,MAAM,qBAA+C,CAA/C,IAAIC,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEAC,IAAAA,2BAAc,EAACnD,KAAK,cAAcyC;YAClCU,IAAAA,2BAAc,EAACnD,KAAK,eAAewB,UAAU4B,KAAK;YAClDD,IAAAA,2BAAc,EAACnD,KAAK,oBAAoB;YAExC,IAAK,MAAMqD,OAAOV,yBAAyB,CAAC,EAAG;gBAC7CQ,IAAAA,2BAAc,EACZnD,KACAqD,KACAV,qBAAsB,CAACU,IAAyB;YAEpD;YAEA5G,MAAM,gBAAgBuD,IAAIU,GAAG,EAAEV,IAAIM,OAAO;YAE1C,IAAI;oBAEMvC;gBADR,MAAMuF,aACJ,OAAMvF,iCAAAA,yBAAAA,aAAcmC,QAAQ,qBAAtBnC,uBAAwBvB,UAAU,CAAC+G;gBAC3C,IAAI;oBACF,OAAMD,8BAAAA,WAAYE,cAAc,CAACxD,KAAKC;gBACxC,EAAE,OAAOwD,KAAK;oBACZ,IAAIA,eAAeC,2BAAe,EAAE;wBAClC,2BAA2B;wBAC3B,MAAMC,cAAcjB,cAAc;wBAClC;oBACF;oBACA,MAAMe;gBACR;gBACA;YACF,EAAE,OAAOG,GAAG;gBACV,qEAAqE;gBACrE,mEAAmE;gBACnE,cAAc;gBACd,IAAIC,IAAAA,0BAAY,EAACD,IAAI;oBACnB;gBACF;gBACA,MAAMA;YACR;QACF;QAEA,MAAMD,gBAAgB,OAAOjB;YAC3B,IAAIA,cAAc,GAAG;gBACnB,MAAM,qBAAkE,CAAlE,IAAIQ,MAAM,CAAC,2CAA2C,EAAElD,IAAIU,GAAG,EAAE,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;YAEA,4BAA4B;YAC5B,IAAI1C,oBAAoB;gBACtB,IAAI8F,IAAAA,8BAAc,EAAC9D,KAAKC,KAAK7C,OAAO2G,iBAAiB,EAAEhH,KAAKsE,QAAQ,GAAG;oBACrE;gBACF;gBACA,MAAM2C,UAAUhE,IAAIU,GAAG,IAAI;gBAE3B,IAAItD,OAAOyD,QAAQ,IAAIoD,IAAAA,4BAAa,EAACD,SAAS5G,OAAOyD,QAAQ,GAAG;oBAC9Db,IAAIU,GAAG,GAAGI,IAAAA,kCAAgB,EAACkD,SAAS5G,OAAOyD,QAAQ;gBACrD;gBACA,MAAMW,YAAYd,YAAG,CAACwD,KAAK,CAAClE,IAAIU,GAAG,IAAI;gBAEvC,MAAMyD,oBAAoB,MAAMnG,mBAAmBoG,WAAW,CAACC,GAAG,CAChErE,KACAC,KACAuB;gBAGF,IAAI2C,kBAAkBG,QAAQ,EAAE;oBAC9B,OAAOH;gBACT;gBACAnE,IAAIU,GAAG,GAAGsD;YACZ;YAEA,MAAM,EACJM,QAAQ,EACR9C,SAAS,EACTQ,UAAU,EACVuC,UAAU,EACVC,UAAU,EACVC,aAAa,EACd,GAAG,MAAMC,cAAc;gBACtB1E;gBACAC;gBACA0E,cAAc;gBACdC,QAAQC,IAAAA,mCAAsB,EAAC5E;gBAC/BqC;YACF;YAEA,IAAIrC,IAAI6E,MAAM,IAAI7E,IAAIqE,QAAQ,EAAE;gBAC9B;YACF;YAEA,IAAItG,sBAAsByG,CAAAA,iCAAAA,cAAeM,IAAI,MAAK,oBAAoB;gBACpE,MAAMf,UAAUhE,IAAIU,GAAG,IAAI;gBAE3B,IAAItD,OAAOyD,QAAQ,IAAIoD,IAAAA,4BAAa,EAACD,SAAS5G,OAAOyD,QAAQ,GAAG;oBAC9Db,IAAIU,GAAG,GAAGI,IAAAA,kCAAgB,EAACkD,SAAS5G,OAAOyD,QAAQ;gBACrD;gBAEA,IAAI0D,YAAY;oBACd,KAAK,MAAMlB,OAAO2B,OAAOC,IAAI,CAACV,YAAa;wBACzCtE,IAAI8B,SAAS,CAACsB,KAAKkB,UAAU,CAAClB,IAAI;oBACpC;gBACF;gBACA,MAAM6B,SAAS,MAAMlH,mBAAmBwF,cAAc,CAACxD,KAAKC;gBAE5D,IAAIiF,OAAOZ,QAAQ,EAAE;oBACnB;gBACF;gBACA,sEAAsE;gBACtEtE,IAAIU,GAAG,GAAGsD;YACZ;YAEAvH,MAAM,mBAAmBuD,IAAIU,GAAG,EAAE;gBAChC+D;gBACAzC;gBACAuC;gBACAC,YAAY,CAAC,CAACA;gBACdhD,WAAW;oBACT5E,UAAU4E,UAAU5E,QAAQ;oBAC5BwG,OAAO5B,UAAU4B,KAAK;gBACxB;gBACAkB;YACF;YAEA,0CAA0C;YAC1C,KAAK,MAAMjB,OAAO2B,OAAOC,IAAI,CAACV,cAAc,CAAC,GAAI;gBAC/CtE,IAAI8B,SAAS,CAACsB,KAAKkB,UAAU,CAAClB,IAAI;YACpC;YAEA,kBAAkB;YAClB,IAAI,CAACmB,cAAcxC,cAAcA,aAAa,OAAOA,aAAa,KAAK;gBACrE,MAAMmD,cAAczE,YAAG,CAAC0E,MAAM,CAAC5D;gBAC/BvB,IAAI+B,UAAU,GAAGA;gBACjB/B,IAAI8B,SAAS,CAAC,YAAYoD;gBAE1B,IAAInD,eAAeC,sCAAkB,CAACoD,iBAAiB,EAAE;oBACvDpF,IAAI8B,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEoD,aAAa;gBACjD;gBACA,OAAOlF,IAAIkC,GAAG,CAACgD;YACjB;YAEA,kCAAkC;YAClC,IAAIX,YAAY;gBACdvE,IAAI+B,UAAU,GAAGA,cAAc;gBAC/B,OAAO,MAAMsD,IAAAA,gCAAkB,EAACd,YAAYvE;YAC9C;YAEA,IAAIqE,YAAY9C,UAAU+D,QAAQ,EAAE;oBAMhC1C;gBALF,OAAO,MAAM2C,IAAAA,0BAAY,EACvBxF,KACAC,KACAuB,WACAiE,YACA5C,kBAAAA,IAAAA,2BAAc,EAAC7C,KAAK,oCAApB6C,gBAAqC6C,eAAe,IACpDtI,OAAOuI,YAAY,CAACC,YAAY;YAEpC;YAEA,IAAInB,CAAAA,iCAAAA,cAAeoB,MAAM,KAAIpB,cAAcqB,QAAQ,EAAE;gBACnD,IACE/I,KAAKI,GAAG,IACPS,CAAAA,UAAUmI,QAAQ,CAACC,GAAG,CAACvB,cAAcqB,QAAQ,KAC5ClI,UAAUqI,SAAS,CAACD,GAAG,CAACvB,cAAcqB,QAAQ,CAAA,GAChD;oBACA7F,IAAI+B,UAAU,GAAG;oBACjB,MAAMkE,UAAU,CAAC,2DAA2D,EAAEzB,cAAcqB,QAAQ,CAAC,8DAA8D,CAAC;oBACpK,MAAMtD,aAAahB,WAAW,WAAWkB,aAAa;wBACpDyD,cAAc;wBACdC,aAAa,qBAAkB,CAAlB,IAAIlD,MAAMgD,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAChC;oBACAG,KAAIC,KAAK,CAACJ;oBACV;gBACF;gBAEA,IACE,CAACjG,IAAIsG,SAAS,CAAC,oBACf9B,cAAcM,IAAI,KAAK,oBACvB;oBACA,IAAIhI,KAAKI,GAAG,IAAI,CAACR,WAAW6E,UAAU5E,QAAQ,GAAG;wBAC/CqD,IAAI8B,SAAS,CAAC,iBAAiB;oBACjC,OAAO;wBACL9B,IAAI8B,SAAS,CACX,iBACA;oBAEJ;gBACF;gBACA,IAAI,CAAE/B,CAAAA,IAAIwG,MAAM,KAAK,SAASxG,IAAIwG,MAAM,KAAK,MAAK,GAAI;oBACpDvG,IAAI8B,SAAS,CAAC,SAAS;wBAAC;wBAAO;qBAAO;oBACtC9B,IAAI+B,UAAU,GAAG;oBACjB,OAAO,MAAMQ,aACX9B,YAAG,CAACwD,KAAK,CAAC,QAAQ,OAClB,QACAxB,aACA;wBACEyD,cAAc;oBAChB;gBAEJ;gBAEA,IAAI;oBACF,OAAO,MAAMM,IAAAA,wBAAW,EAACzG,KAAKC,KAAKwE,cAAcqB,QAAQ,EAAE;wBACzDY,MAAMjC,cAAckC,SAAS;wBAC7B,uEAAuE;wBACvEC,MAAMxJ,OAAOyJ,aAAa;oBAC5B;gBACF,EAAE,OAAOpD,KAAU;oBACjB;;;;;WAKC,GACD,MAAMqD,wCAAwC,IAAIvE,IAAI;wBACpD,kFAAkF;wBAClF,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,kDAAkD;wBAClD,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,gGAAgG;wBAChG,+FAA+F;wBAC/F,qFAAqF;wBACrF,OAAO;wBAEP,8DAA8D;wBAC9D,+FAA+F;wBAC/F;wBAEA,0DAA0D;wBAC1D,+FAA+F;wBAC/F;wBAEA,2DAA2D;wBAC3D,+FAA+F;wBAC/F;qBACD;oBAED,IAAIwE,mBAAmBD,sCAAsCd,GAAG,CAC9DvC,IAAIzB,UAAU;oBAGhB,qCAAqC;oBACrC,IAAI,CAAC+E,kBAAkB;;wBACnBtD,IAAYzB,UAAU,GAAG;oBAC7B;oBAEA,IAAI,OAAOyB,IAAIzB,UAAU,KAAK,UAAU;wBACtC,MAAMS,aAAa,CAAC,CAAC,EAAEgB,IAAIzB,UAAU,EAAE;wBACvC,MAAMmE,eAAe1C,IAAIzB,UAAU;wBACnC/B,IAAI+B,UAAU,GAAGyB,IAAIzB,UAAU;wBAC/B,OAAO,MAAMQ,aACX9B,YAAG,CAACwD,KAAK,CAACzB,YAAY,OACtBA,YACAC,aACA;4BACEyD;wBACF;oBAEJ;oBACA,MAAM1C;gBACR;YACF;YAEA,IAAIgB,eAAe;gBACjBnC,eAAe0E,GAAG,CAACvC,cAAcqB,QAAQ;gBAEzC,OAAO,MAAMtD,aACXhB,WACAA,UAAU5E,QAAQ,IAAI,KACtB8F,aACA;oBACEuE,cAAcxC,cAAcqB,QAAQ;gBACtC;YAEJ;YAEA,WAAW;YACX7F,IAAI8B,SAAS,CACX,iBACA;YAGF,0IAA0I;YAC1I,IAAIhF,KAAKI,GAAG,IAAI,CAACsH,iBAAiBjD,UAAU5E,QAAQ,KAAK,gBAAgB;gBACvEqD,IAAI+B,UAAU,GAAG;gBACjB/B,IAAIkC,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,MAAM+E,cAAcnK,KAAKI,GAAG,GACxBa,sCAAAA,mBAAoBmJ,YAAY,CAACC,cAAc,GAC/C,MAAMxJ,UAAUyJ,OAAO,CAACC,qCAA0B;YAEtDrH,IAAI+B,UAAU,GAAG;YAEjB,IAAIkF,aAAa;gBACf,OAAO,MAAM1E,aACXhB,WACA8F,qCAA0B,EAC1B5E,aACA;oBACEyD,cAAc;gBAChB;YAEJ;YAEA,MAAM3D,aAAahB,WAAW,QAAQkB,aAAa;gBACjDyD,cAAc;YAChB;QACF;QAEA,IAAI;YACF,MAAMxC,cAAc;QACtB,EAAE,OAAOF,KAAK;YACZ,IAAI;gBACF,IAAIhB,aAAa;gBACjB,IAAI0D,eAAe;gBAEnB,IAAI1C,eAAe8D,kBAAW,EAAE;oBAC9B9E,aAAa;oBACb0D,eAAe;gBACjB,OAAO;oBACLqB,QAAQlB,KAAK,CAAC7C;gBAChB;gBACAxD,IAAI+B,UAAU,GAAGyF,OAAOtB;gBACxB,OAAO,MAAM3D,aAAa9B,YAAG,CAACwD,KAAK,CAACzB,YAAY,OAAOA,YAAY,GAAG;oBACpE0D,cAAclG,IAAI+B,UAAU;gBAC9B;YACF,EAAE,OAAO0F,MAAM;gBACbF,QAAQlB,KAAK,CAACoB;YAChB;YACAzH,IAAI+B,UAAU,GAAG;YACjB/B,IAAIkC,GAAG,CAAC;QACV;IACF;IAEA,IAAIqB,iBAAuCrD;IAC3C,IAAI/C,OAAOuI,YAAY,CAACgC,SAAS,EAAE;QACjC,2CAA2C;QAC3C,MAAM,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAE,GACnDvJ,QAAQ;QACVkF,iBAAiBoE,yBAAyBpE;QAC1CqE;QACA,yFAAyF;QACzF3J,gBAAgBC,WAAWC,KAAK;IAClC;IACAtB,eAAe,CAACC,KAAKS,GAAG,CAAC,GAAGgG;IAE5B,MAAMD,mBAA8D;QAClE1D,MAAM9C,KAAK8C,IAAI;QACfrC,KAAKT,KAAKS,GAAG;QACb6D,UAAUtE,KAAKsE,QAAQ;QACvBvD,aAAaf,KAAKe,WAAW;QAC7BX,KAAK,CAAC,CAACJ,KAAKI,GAAG;QACf2K,QAAQ/K,KAAK+K,MAAM;QACnBX,cAAc;YACZ,GAAInJ,CAAAA,sCAAAA,mBAAoBmJ,YAAY,KAAI,CAAC,CAAC;YAC1CY,YAAY,EAAE9J,qCAAAA,kBAAmB8J,YAAY,CAACC,IAAI,CAAC/J;QACrD;QACAgK,uBAAuB,CAAC,CAAC7K,OAAOuI,YAAY,CAACgC,SAAS;QACtDO,yBAAyB,CAAC,CAACnL,KAAKmL,uBAAuB;QACvDC,gBAAgBlK;QAChBmB,iBAAiBrC,KAAKqC,eAAe;QACrCgJ,OAAOrL,KAAKqL,KAAK;QACjBtI,oBAAoB/C,KAAK+C,kBAAkB;IAC7C;IACAyD,iBAAiB4D,YAAY,CAACkB,mBAAmB,GAAGlI;IAEpD,yBAAyB;IACzB,MAAM8C,WAAW,MAAMlF,aAAamC,QAAQ,CAAC1D,UAAU,CAAC+G;IAExD,MAAM+E,WAAW,OACfvD,MACAtB;QAEA,IAAI8E,IAAAA,sBAAU,EAAC9E,MAAM;YACnB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,IAAIsB,SAAS,sBAAsB;YACjCsB,KAAIC,KAAK,CAAC,wBAAwB7C;QACpC,OAAO,IAAIsB,SAAS,qBAAqB;YACvCsB,KAAIC,KAAK,CAAC,uBAAuB7C;QACnC;IACF;IAEAzG,QAAQoF,EAAE,CAAC,qBAAqBkG,SAASN,IAAI,CAAC,MAAM;IACpDhL,QAAQoF,EAAE,CAAC,sBAAsBkG,SAASN,IAAI,CAAC,MAAM;IAErD,MAAMtD,gBAAgB8D,IAAAA,+BAAgB,EACpC5K,WACAR,QACAL,MACAgB,aAAamC,QAAQ,EACrBqD,kBACAvF,sCAAAA,mBAAoByK,gBAAgB;IAGtC,MAAMC,iBAAuC,OAAO1I,KAAK2I,QAAQC;QAC/D,IAAI;YACF5I,IAAIoC,EAAE,CAAC,SAAS,CAACC;YACf,2BAA2B;YAC3B,uBAAuB;YACzB;YACAsG,OAAOvG,EAAE,CAAC,SAAS,CAACC;YAClB,2BAA2B;YAC3B,uBAAuB;YACzB;YAEA,IAAItF,KAAKI,GAAG,IAAIa,sBAAsBgC,IAAIU,GAAG,EAAE;gBAC7C,IACEoD,IAAAA,8BAAc,EAAC9D,KAAK2I,QAAQvL,OAAO2G,iBAAiB,EAAEhH,KAAKsE,QAAQ,GACnE;oBACA;gBACF;gBACA,MAAM,EAAER,QAAQ,EAAEgI,WAAW,EAAE,GAAGzL;gBAElC,IAAI0L,YAAYjI;gBAEhB,8CAA8C;gBAC9C,IAAIgI,aAAa;oBACfC,YAAYC,IAAAA,4CAAqB,EAACF;oBAElC,IAAIG,IAAIC,QAAQ,CAACH,YAAY;wBAC3B,sCAAsC;wBACtC,yCAAyC;wBACzC,yCAAyC;wBACzCA,YAAY,IAAIE,IAAIF,WAAWlM,QAAQ,CAAC8E,OAAO,CAAC,OAAO;oBACzD;gBACF;gBAEA,MAAMwH,eAAelJ,IAAIU,GAAG,CAACkC,UAAU,CACrCuG,IAAAA,sCAAkB,EAAC,GAAGL,UAAU,kBAAkB,CAAC;gBAGrD,0DAA0D;gBAC1D,iEAAiE;gBACjE,IAAII,cAAc;oBAChB,OAAOlL,mBAAmBoG,WAAW,CAACgF,KAAK,CACzCpJ,KACA2I,QACAC,MACA,CAACS;wBACCA,OAAOC,IAAI,CACTC,KAAKC,SAAS,CAAC;4BACbC,QAAQC,6CAA2B,CAACC,YAAY;4BAChDC,MAAM3L,CAAAA,qCAAAA,kBAAmB4L,cAAc,KAAI,CAAC;wBAC9C;oBAEJ;gBAEJ;YACF;YAEA,MAAM5J,MAAM,IAAI6J,2BAAc,CAAC;gBAC7BC,WAAW;oBACT,MAAM,qBAEL,CAFK,IAAI7G,MACR,mFADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAM,EAAEuB,aAAa,EAAEjD,SAAS,EAAE,GAAG,MAAMkD,cAAc;gBACvD1E;gBACAC;gBACA0E,cAAc;gBACdC,QAAQC,IAAAA,mCAAsB,EAAC8D;YACjC;YAEA,mDAAmD;YACnD,oCAAoC;YACpC,IAAIlE,eAAe;gBACjB,OAAOkE,OAAOxG,GAAG;YACnB;YAEA,IAAIX,UAAU+D,QAAQ,EAAE;gBACtB,OAAO,MAAMC,IAAAA,0BAAY,EAACxF,KAAK2I,QAAQnH,WAAWoH;YACpD;QAEA,sEAAsE;QACtE,sDAAsD;QACxD,EAAE,OAAOnF,KAAK;YACZ+D,QAAQlB,KAAK,CAAC,kCAAkC7C;YAChDkF,OAAOxG,GAAG;QACZ;IACF;IAEA,OAAO;QACLqB;QACAkF;QACAZ,QAAQ7E,SAAS6E,MAAM;QACvBkC;gBACEhM;YAAAA,uCAAAA,kCAAAA,mBAAoBoG,WAAW,qBAA/BpG,gCAAiCiM,KAAK;QACxC;IACF;AACF"}