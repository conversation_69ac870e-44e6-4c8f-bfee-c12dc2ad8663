{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-icons.ts"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { Icon, IconDescriptor } from '../types/metadata-types'\nimport type { FieldResolver } from '../types/resolvers'\nimport { resolveAsArrayOrUndefined } from '../generate/utils'\nimport { isStringOrURL } from './resolve-url'\nimport { IconKeys } from '../constants'\n\nexport function resolveIcon(icon: Icon): IconDescriptor {\n  if (isStringOrURL(icon)) return { url: icon }\n  else if (Array.isArray(icon)) return icon\n  return icon\n}\n\nexport const resolveIcons: FieldResolver<'icons'> = (icons) => {\n  if (!icons) {\n    return null\n  }\n\n  const resolved: ResolvedMetadata['icons'] = {\n    icon: [],\n    apple: [],\n  }\n  if (Array.isArray(icons)) {\n    resolved.icon = icons.map(resolveIcon).filter(Boolean)\n  } else if (isStringOrURL(icons)) {\n    resolved.icon = [resolveIcon(icons)]\n  } else {\n    for (const key of IconKeys) {\n      const values = resolveAsArrayOrUndefined(icons[key])\n      if (values) resolved[key] = values.map(resolveIcon)\n    }\n  }\n  return resolved\n}\n"], "names": ["resolveAsArrayOrUndefined", "isStringOrURL", "IconKeys", "resolveIcon", "icon", "url", "Array", "isArray", "resolveIcons", "icons", "resolved", "apple", "map", "filter", "Boolean", "key", "values"], "mappings": "AAGA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,aAAa,QAAQ,gBAAe;AAC7C,SAASC,QAAQ,QAAQ,eAAc;AAEvC,OAAO,SAASC,YAAYC,IAAU;IACpC,IAAIH,cAAcG,OAAO,OAAO;QAAEC,KAAKD;IAAK;SACvC,IAAIE,MAAMC,OAAO,CAACH,OAAO,OAAOA;IACrC,OAAOA;AACT;AAEA,OAAO,MAAMI,eAAuC,CAACC;IACnD,IAAI,CAACA,OAAO;QACV,OAAO;IACT;IAEA,MAAMC,WAAsC;QAC1CN,MAAM,EAAE;QACRO,OAAO,EAAE;IACX;IACA,IAAIL,MAAMC,OAAO,CAACE,QAAQ;QACxBC,SAASN,IAAI,GAAGK,MAAMG,GAAG,CAACT,aAAaU,MAAM,CAACC;IAChD,OAAO,IAAIb,cAAcQ,QAAQ;QAC/BC,SAASN,IAAI,GAAG;YAACD,YAAYM;SAAO;IACtC,OAAO;QACL,KAAK,MAAMM,OAAOb,SAAU;YAC1B,MAAMc,SAAShB,0BAA0BS,KAAK,CAACM,IAAI;YACnD,IAAIC,QAAQN,QAAQ,CAACK,IAAI,GAAGC,OAAOJ,GAAG,CAACT;QACzC;IACF;IACA,OAAOO;AACT,EAAC"}