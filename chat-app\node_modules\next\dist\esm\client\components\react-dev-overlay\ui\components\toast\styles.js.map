{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/toast/styles.ts"], "sourcesContent": ["const styles = `\n  .nextjs-toast {\n    position: fixed;\n    bottom: 16px;\n    left: 16px;\n    max-width: 420px;\n    z-index: 9000;\n    box-shadow: 0px 16px 32px\n      rgba(0, 0, 0, 0.25);\n  }\n\n  @media (max-width: 440px) {\n    .nextjs-toast {\n      max-width: 90vw;\n      left: 5vw;\n    }\n  }\n\n  .nextjs-toast-errors-parent {\n    padding: 16px;\n    border-radius: var(--rounded-4xl);\n    font-weight: 500;\n    color: var(--color-ansi-bright-white);\n    background-color: var(--color-ansi-red);\n  }\n`\n\nexport { styles }\n"], "names": ["styles"], "mappings": "AAAA,MAAMA,SAAU;AA2BhB,SAASA,MAAM,GAAE"}