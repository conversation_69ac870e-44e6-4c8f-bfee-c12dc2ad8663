{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/toast/toast.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cx } from '../../utils/cx'\nexport type ToastProps = React.HTMLProps<HTMLDivElement> & {\n  children?: React.ReactNode\n  onClick?: () => void\n  className?: string\n}\n\nexport const Toast: React.FC<ToastProps> = function Toast({\n  onClick,\n  children,\n  className,\n  ...props\n}) {\n  return (\n    <div\n      {...props}\n      onClick={(e) => {\n        if (!(e.target as HTMLElement).closest('a')) {\n          e.preventDefault()\n        }\n        return onClick?.()\n      }}\n      className={cx('nextjs-toast', className)}\n    >\n      <div data-nextjs-toast-wrapper>{children}</div>\n    </div>\n  )\n}\n"], "names": ["Toast", "onClick", "children", "className", "props", "div", "e", "target", "closest", "preventDefault", "cx", "data-nextjs-toast-wrapper"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;;iEARU;oBACJ;AAOZ,MAAMA,QAA8B,SAASA,MAAM,KAKzD;IALyD,IAAA,EACxDC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACT,GAAGC,OACJ,GALyD;IAMxD,qBACE,qBAACC;QACE,GAAGD,KAAK;QACTH,SAAS,CAACK;YACR,IAAI,CAAC,AAACA,EAAEC,MAAM,CAAiBC,OAAO,CAAC,MAAM;gBAC3CF,EAAEG,cAAc;YAClB;YACA,OAAOR,2BAAAA;QACT;QACAE,WAAWO,IAAAA,MAAE,EAAC,gBAAgBP;kBAE9B,cAAA,qBAACE;YAAIM,2BAAyB;sBAAET;;;AAGtC"}